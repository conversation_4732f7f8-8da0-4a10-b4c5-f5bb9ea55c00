from django.shortcuts import render, redirect
from django.contrib.auth import authenticate, login
from django.contrib.auth.forms import AuthenticationForm
from customers.models import Customer
from products.models import Product
from invoices.models import Invoice
from django.contrib.auth.decorators import login_required
from django.contrib.auth import get_user
from django.db.models import F

def login_view(request):
    if request.method == 'POST':
        form = AuthenticationForm(request, data=request.POST)
        if form.is_valid():
            username = form.cleaned_data.get('username')
            password = form.cleaned_data.get('password')
            user = authenticate(request, username=username, password=password)
            if user is not None:
                login(request, user)
                return redirect('dashboard_home')
    else:
        form = AuthenticationForm()
    return render(request, 'registration/login.html', {'form': form})

@login_required
def dashboard_home(request):
    customers_count = Customer.objects.count()
    products_count = Product.objects.count()
    invoices_count = Invoice.objects.count()
    low_stock_count = Product.objects.filter(quantity__lte=F('min_quantity')).count()
    user = get_user(request)
    return render(request, 'dashboard/home.html', {
        'customers_count': customers_count,
        'products_count': products_count,
        'invoices_count': invoices_count,
        'low_stock_count': low_stock_count,
        'user': user,
    })
