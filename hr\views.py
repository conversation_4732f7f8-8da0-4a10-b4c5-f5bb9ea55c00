from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.utils import timezone
from .models import Employee, Department, Position

@login_required
def hr_dashboard(request):
    """لوحة تحكم شؤون العاملين"""
    context = {
        'total_employees': Employee.objects.filter(status='active').count(),
        'departments_count': Department.objects.filter(is_active=True).count(),
        'positions_count': Position.objects.filter(is_active=True).count(),
        'attendance_today': 0,  # سيتم تحديثه لاحقاً عند إضافة نموذج الحضور
    }
    return render(request, 'hr/dashboard.html', context)
