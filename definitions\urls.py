from django.urls import path
from . import views

app_name = 'definitions'

urlpatterns = [
    # لوحة التحكم
    path('', views.definitions_dashboard, name='dashboard'),

    # تعريف المخازن
    path('warehouses/', views.warehouse_list, name='warehouse_list'),
    path('warehouses/create/', views.warehouse_create, name='warehouse_create'),
    path('warehouses/<int:warehouse_id>/edit/', views.warehouse_edit, name='warehouse_edit'),
    path('warehouses/<int:warehouse_id>/delete/', views.warehouse_delete, name='warehouse_delete'),
    path('warehouses/<int:warehouse_id>/', views.warehouse_detail, name='warehouse_detail'),
    path('warehouses/create/', views.warehouse_create, name='warehouse_create'),
    path('warehouses/<int:warehouse_id>/edit/', views.warehouse_edit, name='warehouse_edit'),
    path('warehouses/<int:warehouse_id>/delete/', views.warehouse_delete, name='warehouse_delete'),
    path('warehouses/<int:warehouse_id>/', views.warehouse_detail, name='warehouse_detail'),

    # فئات الأصناف - قيد التطوير
    # path('categories/', views.category_list, name='category_list'),

    # تعريف الأصناف - قيد التطوير
    # path('products/', views.product_list, name='product_list'),
]
