from django.urls import path
from . import views

app_name = 'definitions'

urlpatterns = [
    # Dashboard
    path('', views.definitions_dashboard, name='dashboard'),
    
    # Currency URLs
    path('currencies/', views.currency_list, name='currency_list'),
    path('currencies/create/', views.currency_create, name='currency_create'),
    path('currencies/<int:pk>/edit/', views.currency_edit, name='currency_edit'),
    path('currencies/<int:pk>/delete/', views.currency_delete, name='currency_delete'),
    
    # Unit URLs
    path('units/', views.unit_list, name='unit_list'),
    path('units/create/', views.unit_create, name='unit_create'),
    path('units/<int:pk>/edit/', views.unit_edit, name='unit_edit'),
    path('units/<int:pk>/delete/', views.unit_delete, name='unit_delete'),
    
    # Category URLs (will be added later)
    # Payment Method URLs (will be added later)
    # Tax Rate URLs (will be added later)
]
