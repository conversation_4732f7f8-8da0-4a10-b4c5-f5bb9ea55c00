from django.db import models
from django.contrib.auth.models import User
from branches.models import Branch

class Department(models.Model):
    """الأقسام"""
    name = models.CharField(max_length=100, verbose_name="اسم القسم")
    code = models.Char<PERSON>ield(max_length=20, unique=True, verbose_name="رمز القسم")
    description = models.TextField(blank=True, verbose_name="الوصف")
    manager = models.ForeignKey('Employee', on_delete=models.SET_NULL, null=True, blank=True, related_name='managed_departments', verbose_name="مدير القسم")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "قسم"
        verbose_name_plural = "الأقسام"

    def __str__(self):
        return self.name

class Position(models.Model):
    """المناصب الوظيفية"""
    name = models.CharField(max_length=100, verbose_name="اسم المنصب")
    code = models.CharField(max_length=20, unique=True, verbose_name="رمز المنصب")
    department = models.ForeignKey(Department, on_delete=models.CASCADE, verbose_name="القسم")
    basic_salary = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="الراتب الأساسي")
    description = models.TextField(blank=True, verbose_name="الوصف")
    is_active = models.BooleanField(default=True, verbose_name="نشط")

    class Meta:
        verbose_name = "منصب وظيفي"
        verbose_name_plural = "المناصب الوظيفية"

    def __str__(self):
        return f"{self.name} - {self.department.name}"

class Employee(models.Model):
    """الموظفين"""
    GENDER_CHOICES = [
        ('male', 'ذكر'),
        ('female', 'أنثى'),
    ]

    MARITAL_STATUS = [
        ('single', 'أعزب'),
        ('married', 'متزوج'),
        ('divorced', 'مطلق'),
        ('widowed', 'أرمل'),
    ]

    STATUS_CHOICES = [
        ('active', 'نشط'),
        ('inactive', 'غير نشط'),
        ('terminated', 'منتهي الخدمة'),
        ('suspended', 'موقوف'),
    ]

    # Personal Information
    employee_id = models.CharField(max_length=20, unique=True, verbose_name="رقم الموظف")
    user = models.OneToOneField(User, on_delete=models.CASCADE, null=True, blank=True, verbose_name="حساب المستخدم")
    first_name = models.CharField(max_length=50, verbose_name="الاسم الأول")
    last_name = models.CharField(max_length=50, verbose_name="اسم العائلة")
    national_id = models.CharField(max_length=20, unique=True, verbose_name="رقم الهوية")
    birth_date = models.DateField(verbose_name="تاريخ الميلاد")
    gender = models.CharField(max_length=10, choices=GENDER_CHOICES, verbose_name="الجنس")
    marital_status = models.CharField(max_length=10, choices=MARITAL_STATUS, verbose_name="الحالة الاجتماعية")

    # Contact Information
    phone = models.CharField(max_length=20, verbose_name="رقم الهاتف")
    email = models.EmailField(verbose_name="البريد الإلكتروني")
    address = models.TextField(verbose_name="العنوان")

    # Work Information
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE, verbose_name="الفرع")
    department = models.ForeignKey(Department, on_delete=models.CASCADE, verbose_name="القسم")
    position = models.ForeignKey(Position, on_delete=models.CASCADE, verbose_name="المنصب")
    hire_date = models.DateField(verbose_name="تاريخ التوظيف")
    salary = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="الراتب")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active', verbose_name="الحالة")

    # Additional Information
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "موظف"
        verbose_name_plural = "الموظفين"
        ordering = ['employee_id']

    def __str__(self):
        return f"{self.employee_id} - {self.first_name} {self.last_name}"

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"
