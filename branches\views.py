from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from .models import Branch

@login_required
def branches_dashboard(request):
    """لوحة تحكم الفروع"""
    context = {
        'total_branches': Branch.objects.filter(is_active=True).count(),
        'headquarters_count': Branch.objects.filter(branch_type='headquarters', is_active=True).count(),
        'branches_count': Branch.objects.filter(branch_type='branch', is_active=True).count(),
        'warehouses_count': Branch.objects.filter(branch_type='warehouse', is_active=True).count(),
    }
    return render(request, 'branches/dashboard.html', context)
