# Generated by Django 5.2.4 on 2025-07-11 04:51

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('definitions', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='AssetCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم التصنيف')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='رمز التصنيف')),
                ('depreciation_rate', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='معدل الاستهلاك السنوي %')),
                ('useful_life_years', models.IntegerField(default=5, verbose_name='العمر الافتراضي (سنوات)')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'تصنيف أصل',
                'verbose_name_plural': 'تصنيفات الأصول',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Asset',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم الأصل')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='رمز الأصل')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('purchase_price', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='سعر الشراء')),
                ('purchase_date', models.DateField(verbose_name='تاريخ الشراء')),
                ('useful_life_years', models.IntegerField(verbose_name='العمر الافتراضي (سنوات)')),
                ('salvage_value', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='القيمة المتبقية')),
                ('current_value', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='القيمة الحالية')),
                ('accumulated_depreciation', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='مجمع الاستهلاك')),
                ('status', models.CharField(choices=[('active', 'نشط'), ('maintenance', 'تحت الصيانة'), ('disposed', 'تم التخلص منه'), ('sold', 'تم البيع')], default='active', max_length=20, verbose_name='الحالة')),
                ('location', models.CharField(blank=True, max_length=200, verbose_name='الموقع')),
                ('serial_number', models.CharField(blank=True, max_length=100, verbose_name='الرقم التسلسلي')),
                ('manufacturer', models.CharField(blank=True, max_length=100, verbose_name='الشركة المصنعة')),
                ('model', models.CharField(blank=True, max_length=100, verbose_name='الموديل')),
                ('last_maintenance_date', models.DateField(blank=True, null=True, verbose_name='تاريخ آخر صيانة')),
                ('next_maintenance_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الصيانة القادمة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('currency', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.currency', verbose_name='العملة')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='assets.assetcategory', verbose_name='التصنيف')),
            ],
            options={
                'verbose_name': 'أصل ثابت',
                'verbose_name_plural': 'الأصول الثابتة',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='AssetMaintenance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('maintenance_type', models.CharField(choices=[('preventive', 'صيانة وقائية'), ('corrective', 'صيانة إصلاحية'), ('emergency', 'صيانة طارئة')], max_length=20, verbose_name='نوع الصيانة')),
                ('description', models.TextField(verbose_name='وصف الصيانة')),
                ('maintenance_date', models.DateField(verbose_name='تاريخ الصيانة')),
                ('cost', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='التكلفة')),
                ('vendor', models.CharField(blank=True, max_length=200, verbose_name='مقدم الخدمة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('asset', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='assets.asset', verbose_name='الأصل')),
            ],
            options={
                'verbose_name': 'صيانة أصل',
                'verbose_name_plural': 'صيانة الأصول',
                'ordering': ['-maintenance_date'],
            },
        ),
    ]
