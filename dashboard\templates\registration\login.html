<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Modern</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            color: #333;
        }

        .login-container {
            background-color: #fff;
            padding: 45px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            width: 380px;
            text-align: center;
            animation: fadeIn 0.8s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        h2 {
            color: #333;
            margin-bottom: 30px;
            font-size: 28px;
            font-weight: 700;
        }

        .form-group {
            margin-bottom: 25px;
            text-align: left;
        }

        label {
            display: block;
            color: #555;
            font-weight: 600;
            margin-bottom: 8px;
            font-size: 15px;
        }

        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            color: #333;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
            box-sizing: border-box; /* Include padding in width */
        }

        .form-group input:focus {
            border-color: #007bff;
            box-shadow: 0 0 8px rgba(0, 123, 255, 0.2);
            outline: none;
        }

        .password-container {
            position: relative;
        }

        .password-container input {
            padding-right: 40px; /* Make space for the icon */
        }

        .toggle-password {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: #888;
            font-size: 18px;
            transition: color 0.3s ease;
        }

        .toggle-password:hover {
            color: #333;
        }

        button[type="submit"] {
            width: 100%;
            padding: 14px;
            background-color: #007bff;
            color: #fff;
            border: none;
            border-radius: 8px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.3s ease, box-shadow 0.3s ease;
        }

        button[type="submit"]:hover {
            background-color: #0056b3;
            box-shadow: 0 4px 15px rgba(0, 90, 179, 0.3);
        }

        .signup-link {
            margin-top: 25px;
            font-size: 15px;
            color: #555;
        }

        .signup-link a {
            color: #007bff;
            text-decoration: none;
            font-weight: 600;
            transition: color 0.3s ease;
        }

        .signup-link a:hover {
            color: #0056b3;
            text-decoration: underline;
        }

        /* Style for form errors */
        .errorlist {
            color: #dc3545;
            text-align: left;
            margin-bottom: 15px;
            padding: 0;
            list-style: none;
            font-size: 14px;
        }

        .errorlist li {
            margin-bottom: 5px;
        }

    </style>
</head>
<body>
    <div class="login-container">
        <h2>Welcome Back</h2>
        <form method="post">
            {% csrf_token %}
            {% if form.non_field_errors %}
                <ul class="errorlist">
                    {% for error in form.non_field_errors %}
                        <li>{{ error }}</li>
                    {% endfor %}
                </ul>
            {% endif %}
            <div class="form-group">
                <label for="{{ form.username.id_for_label }}">Username</label>
                {{ form.username }}
                {% if form.username.errors %}
                    <ul class="errorlist">
                        {% for error in form.username.errors %}
                            <li>{{ error }}</li>
                        {% endfor %}
                    </ul>
                {% endif %}
            </div>
            <div class="form-group password-container">
                <label for="{{ form.password.id_for_label }}">Password</label>
                {{ form.password }}
                <span class="toggle-password" onclick="togglePasswordVisibility()">👁️</span>
                {% if form.password.errors %}
                    <ul class="errorlist">
                        {% for error in form.password.errors %}
                            <li>{{ error }}</li>
                        {% endfor %}
                    </ul>
                {% endif %}
            </div>
            <button type="submit">Login</button>
        </form>
        <div class="signup-link">
            Don't have an account? <a href="{% url 'customers:signup' %}">Sign up here</a>
        </div>
    </div>
    <script>
        function togglePasswordVisibility() {
            var passwordInput = document.getElementById("id_password");
            var toggleIcon = document.querySelector(".toggle-password");

            if (passwordInput.type === "password") {
                passwordInput.type = "text";
                toggleIcon.textContent = "🔒"; // Change icon to lock
            } else {
                passwordInput.type = "password";
                toggleIcon.textContent = "👁️"; // Change icon to eye
            }
        }

        // Add CSS class to form inputs for easier styling
        document.addEventListener("DOMContentLoaded", function() {
            const form = document.querySelector(".login-container form");
            if (form) {
                const inputs = form.querySelectorAll("input");
                inputs.forEach(input => {
                    // Check if input is not already handled (like the password field specifically)
                    if (!input.parentElement.classList.contains('password-container')) {
                         // Add form-control class if needed, or rely on form-group input style
                         // input.classList.add("form-control");
                    }
                });
            }
        });
    </script>
</body>
</html>
