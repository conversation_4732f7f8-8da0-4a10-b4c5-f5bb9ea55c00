from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Sum, Count, F
from django.utils import timezone
from datetime import datetime, timedelta
from .models import (
    Warehouse, WarehouseZone, WarehouseLocation, Stock,
    StockMovement, StockTransfer, StockAdjustment
)
from .forms import (
    WarehouseForm, WarehouseZoneForm, WarehouseLocationForm,
    StockForm, StockMovementForm, StockTransferForm, StockAdjustmentForm
)

@login_required
def warehouses_dashboard(request):
    """لوحة تحكم المخازن المتقدمة"""

    # إحصائيات عامة
    total_warehouses = Warehouse.objects.count()
    active_warehouses = Warehouse.objects.filter(is_active=True).count()
    total_products = Stock.objects.values('product').distinct().count()
    total_stock_value = Stock.objects.aggregate(
        total=Sum('quantity'))['total'] or 0
    low_stock_items = Stock.objects.filter(
        quantity__lte=F('minimum_stock')).count()

    # إحصائيات إضافية
    out_of_stock = Stock.objects.filter(quantity=0).count()
    reserved_stock = Stock.objects.aggregate(
        total=Sum('reserved_quantity'))['total'] or 0
    available_stock = total_stock_value - reserved_stock
    total_locations = WarehouseLocation.objects.filter(is_active=True).count()

    # حركات اليوم
    today = timezone.now().date()
    today_movements = StockMovement.objects.filter(
        created_at__date=today).count()

    # التحويلات المعلقة
    pending_transfers = StockTransfer.objects.filter(
        status__in=['pending', 'in_transit']).count()

    # إحصائيات حسب نوع المخزن
    warehouse_stats = Warehouse.objects.filter(is_active=True).values(
        'warehouse_type').annotate(count=Count('id'))

    # آخر حركات المخزون
    recent_movements = StockMovement.objects.select_related(
        'product', 'warehouse').order_by('-created_at')[:10]

    # المنتجات التي تحتاج إعادة طلب
    reorder_items = Stock.objects.filter(
        quantity__lte=F('reorder_point')).select_related(
        'product', 'warehouse')[:10]

    # قائمة المخازن للنماذج
    warehouses = Warehouse.objects.filter(is_active=True)

    context = {
        'total_warehouses': total_warehouses,
        'active_warehouses': active_warehouses,
        'total_products': total_products,
        'total_stock_value': total_stock_value,
        'low_stock_items': low_stock_items,
        'out_of_stock': out_of_stock,
        'reserved_stock': reserved_stock,
        'available_stock': available_stock,
        'total_locations': total_locations,
        'today_movements': today_movements,
        'pending_transfers': pending_transfers,
        'warehouse_stats': warehouse_stats,
        'recent_movements': recent_movements,
        'reorder_items': reorder_items,
        'warehouses': warehouses,
        'today': today,
    }
    return render(request, 'warehouses/dashboard.html', context)

# Warehouse Views
@login_required
def warehouse_list(request):
    search_query = request.GET.get('search', '')
    warehouse_type = request.GET.get('type', '')

    warehouses = Warehouse.objects.all()

    if search_query:
        warehouses = warehouses.filter(
            Q(name__icontains=search_query) |
            Q(code__icontains=search_query) |
            Q(manager_name__icontains=search_query)
        )

    if warehouse_type:
        warehouses = warehouses.filter(warehouse_type=warehouse_type)

    warehouses = warehouses.order_by('-created_at')

    paginator = Paginator(warehouses, 12)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'warehouse_type': warehouse_type,
        'warehouse_types': Warehouse.WAREHOUSE_TYPES,
        'total_warehouses': Warehouse.objects.count()
    }
    return render(request, 'warehouses/warehouse_list.html', context)

@login_required
def warehouse_create(request):
    if request.method == 'POST':
        form = WarehouseForm(request.POST)
        if form.is_valid():
            warehouse = form.save()
            messages.success(request, f'تم إضافة المخزن "{warehouse.name}" بنجاح!')
            return redirect('warehouses:warehouse_list')
    else:
        form = WarehouseForm()

    return render(request, 'warehouses/warehouse_form.html', {
        'form': form,
        'title': 'إضافة مخزن جديد',
        'button_text': 'إضافة المخزن'
    })

@login_required
def warehouse_edit(request, pk):
    warehouse = get_object_or_404(Warehouse, pk=pk)

    if request.method == 'POST':
        form = WarehouseForm(request.POST, instance=warehouse)
        if form.is_valid():
            warehouse = form.save()
            messages.success(request, f'تم تحديث المخزن "{warehouse.name}" بنجاح!')
            return redirect('warehouses:warehouse_list')
    else:
        form = WarehouseForm(instance=warehouse)

    return render(request, 'warehouses/warehouse_form.html', {
        'form': form,
        'warehouse': warehouse,
        'title': f'تعديل المخزن: {warehouse.name}',
        'button_text': 'حفظ التغييرات'
    })

@login_required
def warehouse_delete(request, pk):
    warehouse = get_object_or_404(Warehouse, pk=pk)

    if request.method == 'POST':
        warehouse_name = warehouse.name
        warehouse.delete()
        messages.success(request, f'تم حذف المخزن "{warehouse_name}" بنجاح!')
        return redirect('warehouses:warehouse_list')

    return render(request, 'warehouses/warehouse_confirm_delete.html', {'warehouse': warehouse})

@login_required
def warehouse_detail(request, pk):
    warehouse = get_object_or_404(Warehouse, pk=pk)

    # إحصائيات المخزن
    total_products = Stock.objects.filter(warehouse=warehouse).count()
    total_quantity = Stock.objects.filter(warehouse=warehouse).aggregate(
        total=Sum('quantity'))['total'] or 0
    low_stock_count = Stock.objects.filter(
        warehouse=warehouse, quantity__lte=F('minimum_stock')).count()

    # آخر حركات المخزون
    recent_movements = StockMovement.objects.filter(
        warehouse=warehouse).select_related('product').order_by('-created_at')[:10]

    # المناطق والمواقع
    zones = WarehouseZone.objects.filter(warehouse=warehouse, is_active=True)
    locations = WarehouseLocation.objects.filter(
        zone__warehouse=warehouse, is_active=True).count()

    context = {
        'warehouse': warehouse,
        'total_products': total_products,
        'total_quantity': total_quantity,
        'low_stock_count': low_stock_count,
        'recent_movements': recent_movements,
        'zones': zones,
        'locations_count': locations,
    }
    return render(request, 'warehouses/warehouse_detail.html', context)

# Stock Views
@login_required
def stock_list(request):
    search_query = request.GET.get('search', '')
    warehouse_filter = request.GET.get('warehouse', '')
    low_stock_only = request.GET.get('low_stock', '')

    stocks = Stock.objects.select_related('product', 'warehouse', 'location')

    if search_query:
        stocks = stocks.filter(
            Q(product__name__icontains=search_query) |
            Q(product__code__icontains=search_query) |
            Q(warehouse__name__icontains=search_query)
        )

    if warehouse_filter:
        stocks = stocks.filter(warehouse_id=warehouse_filter)

    if low_stock_only:
        stocks = stocks.filter(quantity__lte=F('minimum_stock'))

    stocks = stocks.order_by('product__name')

    paginator = Paginator(stocks, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    warehouses = Warehouse.objects.filter(is_active=True)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'warehouse_filter': warehouse_filter,
        'low_stock_only': low_stock_only,
        'warehouses': warehouses,
        'total_stocks': Stock.objects.count()
    }
    return render(request, 'warehouses/stock_list.html', context)

@login_required
def stock_movements(request):
    search_query = request.GET.get('search', '')
    warehouse_filter = request.GET.get('warehouse', '')
    movement_type = request.GET.get('movement_type', '')

    movements = StockMovement.objects.select_related('product', 'warehouse', 'location')

    if search_query:
        movements = movements.filter(
            Q(product__name__icontains=search_query) |
            Q(reference_number__icontains=search_query)
        )

    if warehouse_filter:
        movements = movements.filter(warehouse_id=warehouse_filter)

    if movement_type:
        movements = movements.filter(movement_type=movement_type)

    movements = movements.order_by('-created_at')

    paginator = Paginator(movements, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    warehouses = Warehouse.objects.filter(is_active=True)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'warehouse_filter': warehouse_filter,
        'movement_type': movement_type,
        'warehouses': warehouses,
        'movement_types': StockMovement.MOVEMENT_TYPES,
        'total_movements': StockMovement.objects.count()
    }
    return render(request, 'warehouses/stock_movements.html', context)

@login_required
def stock_transfers(request):
    search_query = request.GET.get('search', '')
    status_filter = request.GET.get('status', '')

    transfers = StockTransfer.objects.select_related('from_warehouse', 'to_warehouse')

    if search_query:
        transfers = transfers.filter(
            Q(transfer_number__icontains=search_query) |
            Q(from_warehouse__name__icontains=search_query) |
            Q(to_warehouse__name__icontains=search_query)
        )

    if status_filter:
        transfers = transfers.filter(status=status_filter)

    transfers = transfers.order_by('-created_at')

    paginator = Paginator(transfers, 15)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'status_filter': status_filter,
        'status_choices': StockTransfer.STATUS_CHOICES,
        'total_transfers': StockTransfer.objects.count()
    }
    return render(request, 'warehouses/stock_transfers.html', context)
