from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Sum, Count, Q
from django.utils import timezone
from django.core.paginator import Paginator
from django.http import JsonResponse
from definitions.models import WarehouseDefinition, ProductDefinition
from .models import InventoryTransaction, InventoryBalance, StockAdjustment, StockAdjustmentItem
from decimal import Decimal

@login_required
def warehouses_dashboard(request):
    """لوحة تحكم المخازن"""
    # إحصائيات عامة
    total_warehouses = WarehouseDefinition.objects.filter(is_active=True).count()
    total_products = ProductDefinition.objects.filter(is_active=True).count()
    total_transactions_today = InventoryTransaction.objects.filter(
        transaction_date__date=timezone.now().date()
    ).count()

    # إحصائيات المخزون
    total_inventory_value = InventoryBalance.objects.aggregate(
        total=Sum('total_cost')
    )['total'] or 0

    # المنتجات تحت الحد الأدنى
    low_stock_products = []
    for balance in InventoryBalance.objects.select_related('product', 'warehouse'):
        if balance.product.minimum_stock > 0 and balance.quantity <= balance.product.minimum_stock:
            low_stock_products.append(balance)

    # آخر الحركات
    recent_transactions = InventoryTransaction.objects.select_related(
        'warehouse', 'product', 'created_by'
    ).order_by('-created_at')[:10]

    # المخازن مع أرصدتها
    warehouses_data = []
    for warehouse in WarehouseDefinition.objects.filter(is_active=True):
        warehouse_value = InventoryBalance.objects.filter(
            warehouse=warehouse
        ).aggregate(total=Sum('total_cost'))['total'] or 0

        warehouse_products = InventoryBalance.objects.filter(
            warehouse=warehouse,
            quantity__gt=0
        ).count()

        warehouses_data.append({
            'warehouse': warehouse,
            'total_value': warehouse_value,
            'products_count': warehouse_products
        })

    context = {
        'total_warehouses': total_warehouses,
        'total_products': total_products,
        'total_transactions_today': total_transactions_today,
        'total_inventory_value': total_inventory_value,
        'low_stock_products': low_stock_products[:5],  # أول 5 فقط
        'recent_transactions': recent_transactions,
        'warehouses_data': warehouses_data,
    }

    return render(request, 'warehouses/dashboard.html', context)

@login_required
def inventory_list(request):
    """قائمة أرصدة المخزون"""
    warehouse_id = request.GET.get('warehouse')
    search_query = request.GET.get('search')

    balances = InventoryBalance.objects.select_related('warehouse', 'product')

    if warehouse_id:
        balances = balances.filter(warehouse_id=warehouse_id)

    if search_query:
        balances = balances.filter(
            Q(product__name__icontains=search_query) |
            Q(product__code__icontains=search_query)
        )

    # فلترة المنتجات التي لها رصيد
    balances = balances.filter(quantity__gt=0)

    paginator = Paginator(balances, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    warehouses = WarehouseDefinition.objects.filter(is_active=True)

    context = {
        'page_obj': page_obj,
        'warehouses': warehouses,
        'selected_warehouse': warehouse_id,
        'search_query': search_query,
    }

    return render(request, 'warehouses/inventory_list.html', context)

@login_required
def add_stock(request):
    """إضافة مخزون (إذن إدخال)"""
    if request.method == 'POST':
        warehouse_id = request.POST.get('warehouse')
        product_id = request.POST.get('product')
        quantity = Decimal(request.POST.get('quantity', '0'))
        unit_cost = Decimal(request.POST.get('unit_cost', '0'))
        reason = request.POST.get('reason', 'purchase')
        reference_number = request.POST.get('reference_number', '')
        notes = request.POST.get('notes', '')

        warehouse = get_object_or_404(WarehouseDefinition, id=warehouse_id)
        product = get_object_or_404(ProductDefinition, id=product_id)

        # إنشاء حركة إدخال
        transaction = InventoryTransaction.objects.create(
            warehouse=warehouse,
            product=product,
            transaction_type='in',
            transaction_reason=reason,
            quantity=quantity,
            unit_cost=unit_cost,
            reference_number=reference_number,
            notes=notes,
            transaction_date=timezone.now(),
            created_by=request.user
        )

        messages.success(request, f'تم إضافة {quantity} من {product.name} إلى {warehouse.name} بنجاح')
        return redirect('warehouses:add_stock')

    warehouses = WarehouseDefinition.objects.filter(is_active=True)
    products = ProductDefinition.objects.filter(is_active=True)

    context = {
        'warehouses': warehouses,
        'products': products,
    }

    return render(request, 'warehouses/add_stock.html', context)

@login_required
def remove_stock(request):
    """صرف مخزون (إذن إخراج)"""
    if request.method == 'POST':
        warehouse_id = request.POST.get('warehouse')
        product_id = request.POST.get('product')
        quantity = Decimal(request.POST.get('quantity', '0'))
        reason = request.POST.get('reason', 'sale')
        reference_number = request.POST.get('reference_number', '')
        notes = request.POST.get('notes', '')

        warehouse = get_object_or_404(WarehouseDefinition, id=warehouse_id)
        product = get_object_or_404(ProductDefinition, id=product_id)

        # التحقق من توفر الكمية
        try:
            balance = InventoryBalance.objects.get(warehouse=warehouse, product=product)
            if balance.quantity < quantity:
                messages.error(request, f'الكمية المطلوبة ({quantity}) أكبر من المتوفر ({balance.quantity})')
                return redirect('warehouses:remove_stock')
        except InventoryBalance.DoesNotExist:
            messages.error(request, 'لا يوجد رصيد لهذا المنتج في المخزن المحدد')
            return redirect('warehouses:remove_stock')

        # حساب متوسط التكلفة
        unit_cost = balance.average_cost

        # إنشاء حركة إخراج
        transaction = InventoryTransaction.objects.create(
            warehouse=warehouse,
            product=product,
            transaction_type='out',
            transaction_reason=reason,
            quantity=quantity,
            unit_cost=unit_cost,
            reference_number=reference_number,
            notes=notes,
            transaction_date=timezone.now(),
            created_by=request.user
        )

        messages.success(request, f'تم صرف {quantity} من {product.name} من {warehouse.name} بنجاح')
        return redirect('warehouses:remove_stock')

    warehouses = WarehouseDefinition.objects.filter(is_active=True)
    products = ProductDefinition.objects.filter(is_active=True)

    context = {
        'warehouses': warehouses,
        'products': products,
    }

    return render(request, 'warehouses/remove_stock.html', context)

# باقي الـ views - سيتم إضافتها تدريجياً
@login_required
def transactions_list(request):
    """قائمة حركات المخزون"""
    messages.info(request, 'هذه الصفحة قيد التطوير')
    return redirect('warehouses:dashboard')

@login_required
def low_stock_report(request):
    """تقرير المخزون المنخفض"""
    messages.info(request, 'هذه الصفحة قيد التطوير')
    return redirect('warehouses:dashboard')

@login_required
def warehouse_report(request, warehouse_id):
    """تقرير مخزن محدد"""
    messages.info(request, 'هذه الصفحة قيد التطوير')
    return redirect('warehouses:dashboard')

@login_required
def adjustments_list(request):
    """قائمة تسويات المخزون"""
    messages.info(request, 'هذه الصفحة قيد التطوير')
    return redirect('warehouses:dashboard')

@login_required
def create_adjustment(request):
    """إنشاء تسوية مخزون"""
    messages.info(request, 'هذه الصفحة قيد التطوير')
    return redirect('warehouses:dashboard')

@login_required
def adjustment_detail(request, adjustment_id):
    """تفاصيل تسوية المخزون"""
    messages.info(request, 'هذه الصفحة قيد التطوير')
    return redirect('warehouses:dashboard')

@login_required
def transfer_stock(request):
    """نقل مخزون بين المخازن"""
    if request.method == 'POST':
        messages.info(request, 'هذه الميزة قيد التطوير')
        return redirect('warehouses:transfer_stock')

    warehouses = WarehouseDefinition.objects.filter(is_active=True)
    products = ProductDefinition.objects.filter(is_active=True)

    context = {
        'warehouses': warehouses,
        'products': products,
    }

    return render(request, 'warehouses/transfer_stock.html', context)

@login_required
def get_product_balance(request):
    """API للحصول على رصيد منتج في مخزن"""
    warehouse_id = request.GET.get('warehouse_id')
    product_id = request.GET.get('product_id')

    try:
        balance = InventoryBalance.objects.get(
            warehouse_id=warehouse_id,
            product_id=product_id
        )
        data = {
            'quantity': float(balance.quantity),
            'average_cost': float(balance.average_cost),
        }
    except InventoryBalance.DoesNotExist:
        data = {
            'quantity': 0,
            'average_cost': 0,
        }

    return JsonResponse(data)

@login_required
def get_warehouse_products(request):
    """API للحصول على منتجات مخزن"""
    warehouse_id = request.GET.get('warehouse_id')

    balances = InventoryBalance.objects.filter(
        warehouse_id=warehouse_id,
        quantity__gt=0
    ).select_related('product')

    products = []
    for balance in balances:
        products.append({
            'id': balance.product.id,
            'name': balance.product.name,
            'quantity': float(balance.quantity),
        })

    return JsonResponse({'products': products})
