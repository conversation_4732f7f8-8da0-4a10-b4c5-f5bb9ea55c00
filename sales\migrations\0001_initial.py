# Generated by Django 5.2.4 on 2025-07-11 04:51

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('customers', '0001_initial'),
        ('definitions', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='SalesOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الطلب')),
                ('order_date', models.DateField(verbose_name='تاريخ الطلب')),
                ('delivery_date', models.DateField(blank=True, null=True, verbose_name='تاريخ التسليم المتوقع')),
                ('status', models.CharField(choices=[('draft', 'مسودة'), ('confirmed', 'مؤكد'), ('shipped', 'تم الشحن'), ('delivered', 'تم التسليم'), ('cancelled', 'ملغي')], default='draft', max_length=20, verbose_name='الحالة')),
                ('subtotal', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='المجموع الفرعي')),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='مبلغ الضريبة')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='المجموع الكلي')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('currency', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.currency', verbose_name='العملة')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sales_orders', to='customers.customer', verbose_name='العميل')),
            ],
            options={
                'verbose_name': 'أمر بيع',
                'verbose_name_plural': 'أوامر البيع',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SalesInvoice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('invoice_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الفاتورة')),
                ('invoice_date', models.DateField(verbose_name='تاريخ الفاتورة')),
                ('due_date', models.DateField(verbose_name='تاريخ الاستحقاق')),
                ('status', models.CharField(choices=[('draft', 'مسودة'), ('sent', 'مرسلة'), ('paid', 'مدفوعة'), ('overdue', 'متأخرة'), ('cancelled', 'ملغية')], default='draft', max_length=20, verbose_name='الحالة')),
                ('subtotal', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='المجموع الفرعي')),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='مبلغ الضريبة')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='المجموع الكلي')),
                ('paid_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='المبلغ المدفوع')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('currency', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.currency', verbose_name='العملة')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sales_invoices', to='customers.customer', verbose_name='العميل')),
                ('sales_order', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='sales.salesorder', verbose_name='أمر البيع')),
            ],
            options={
                'verbose_name': 'فاتورة مبيعات',
                'verbose_name_plural': 'فواتير المبيعات',
                'ordering': ['-created_at'],
            },
        ),
    ]
