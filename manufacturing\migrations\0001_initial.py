# Generated by Django 5.2.4 on 2025-07-11 12:37

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('products', '0001_initial'),
        ('warehouses', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ProductionOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_number', models.CharField(max_length=50, unique=True, verbose_name='رقم أمر الإنتاج')),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0.01)], verbose_name='الكمية المطلوبة')),
                ('status', models.CharField(choices=[('draft', 'مسودة'), ('confirmed', 'مؤكد'), ('in_progress', 'قيد التنفيذ'), ('completed', 'مكتمل'), ('cancelled', 'ملغي')], default='draft', max_length=20, verbose_name='الحالة')),
                ('priority', models.CharField(choices=[('low', 'منخفضة'), ('normal', 'عادية'), ('high', 'عالية'), ('urgent', 'عاجلة')], default='normal', max_length=20, verbose_name='الأولوية')),
                ('planned_start_date', models.DateTimeField(verbose_name='تاريخ البدء المخطط')),
                ('planned_end_date', models.DateTimeField(verbose_name='تاريخ الانتهاء المخطط')),
                ('actual_start_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ البدء الفعلي')),
                ('actual_end_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الانتهاء الفعلي')),
                ('total_material_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي تكلفة المواد')),
                ('total_labor_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي تكلفة العمالة')),
                ('total_overhead_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي التكاليف الإضافية')),
                ('total_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي التكلفة')),
                ('unit_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='تكلفة الوحدة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='products.product', verbose_name='المنتج المراد تصنيعه')),
                ('target_warehouse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='warehouses.warehouse', verbose_name='مخزن الإنتاج النهائي')),
            ],
            options={
                'verbose_name': 'أمر إنتاج',
                'verbose_name_plural': 'أوامر الإنتاج',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ProductionOperation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('operation_name', models.CharField(max_length=100, verbose_name='اسم العملية')),
                ('description', models.TextField(blank=True, verbose_name='وصف العملية')),
                ('estimated_hours', models.DecimalField(decimal_places=2, max_digits=8, validators=[django.core.validators.MinValueValidator(0)], verbose_name='الساعات المقدرة')),
                ('actual_hours', models.DecimalField(decimal_places=2, default=0, max_digits=8, verbose_name='الساعات الفعلية')),
                ('hourly_rate', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='معدل الساعة')),
                ('total_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي التكلفة')),
                ('sequence', models.PositiveIntegerField(default=1, verbose_name='التسلسل')),
                ('status', models.CharField(choices=[('pending', 'في الانتظار'), ('in_progress', 'قيد التنفيذ'), ('completed', 'مكتمل'), ('cancelled', 'ملغي')], default='pending', max_length=20, verbose_name='الحالة')),
                ('start_time', models.DateTimeField(blank=True, null=True, verbose_name='وقت البدء')),
                ('end_time', models.DateTimeField(blank=True, null=True, verbose_name='وقت الانتهاء')),
                ('assigned_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='مسند إلى')),
                ('production_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='operations', to='manufacturing.productionorder', verbose_name='أمر الإنتاج')),
            ],
            options={
                'verbose_name': 'عملية إنتاج',
                'verbose_name_plural': 'عمليات الإنتاج',
                'ordering': ['sequence'],
            },
        ),
        migrations.CreateModel(
            name='ProductionReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('produced_quantity', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الكمية المنتجة')),
                ('waste_quantity', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='كمية الفاقد')),
                ('efficiency_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='نسبة الكفاءة')),
                ('actual_material_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='التكلفة الفعلية للمواد')),
                ('actual_labor_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='التكلفة الفعلية للعمالة')),
                ('actual_overhead_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='التكلفة الفعلية الإضافية')),
                ('actual_total_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي التكلفة الفعلية')),
                ('variance_material', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='انحراف تكلفة المواد')),
                ('variance_labor', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='انحراف تكلفة العمالة')),
                ('variance_total', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي الانحراف')),
                ('report_date', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التقرير')),
                ('generated_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('production_order', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='report', to='manufacturing.productionorder', verbose_name='أمر الإنتاج')),
            ],
            options={
                'verbose_name': 'تقرير إنتاج',
                'verbose_name_plural': 'تقارير الإنتاج',
            },
        ),
        migrations.CreateModel(
            name='QualityCheck',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('check_name', models.CharField(max_length=100, verbose_name='اسم الفحص')),
                ('description', models.TextField(blank=True, verbose_name='وصف الفحص')),
                ('status', models.CharField(choices=[('pending', 'في الانتظار'), ('passed', 'نجح'), ('failed', 'فشل'), ('rework', 'إعادة عمل')], default='pending', max_length=20, verbose_name='النتيجة')),
                ('checked_quantity', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الكمية المفحوصة')),
                ('passed_quantity', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الكمية الناجحة')),
                ('failed_quantity', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الكمية الفاشلة')),
                ('check_date', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الفحص')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('checked_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='فحص بواسطة')),
                ('production_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='quality_checks', to='manufacturing.productionorder', verbose_name='أمر الإنتاج')),
            ],
            options={
                'verbose_name': 'فحص جودة',
                'verbose_name_plural': 'فحوصات الجودة',
                'ordering': ['-check_date'],
            },
        ),
        migrations.CreateModel(
            name='ProductionOrderMaterial',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('required_quantity', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0.01)], verbose_name='الكمية المطلوبة')),
                ('available_quantity', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الكمية المتاحة')),
                ('unit_cost', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='تكلفة الوحدة')),
                ('total_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي التكلفة')),
                ('allocated', models.BooleanField(default=False, verbose_name='تم التخصيص')),
                ('consumed', models.BooleanField(default=False, verbose_name='تم الاستهلاك')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإضافة')),
                ('material', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='products.product', verbose_name='المادة الخام')),
                ('production_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='materials', to='manufacturing.productionorder', verbose_name='أمر الإنتاج')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='warehouses.warehouse', verbose_name='مخزن المادة الخام')),
            ],
            options={
                'verbose_name': 'مادة أمر الإنتاج',
                'verbose_name_plural': 'مواد أوامر الإنتاج',
                'unique_together': {('production_order', 'material', 'warehouse')},
            },
        ),
    ]
