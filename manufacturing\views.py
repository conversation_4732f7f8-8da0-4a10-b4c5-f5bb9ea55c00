from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Sum, Count, F, Avg
from django.utils import timezone
from django.http import JsonResponse
from datetime import datetime, timedelta
from decimal import Decimal
import json

from .models import (
    ProductionOrder, ProductionOrderMaterial, ProductionOperation,
    QualityCheck, ProductionReport
)
from .forms import (
    ProductionOrderForm, ProductionOrderMaterialFormSet,
    ProductionOperationFormSet, QualityCheckForm, ProductionOrderSearchForm
)
from products.models import Product
from warehouses.models import Warehouse, Stock

@login_required
def manufacturing_dashboard(request):
    """لوحة تحكم التصنيع"""

    # إحصائيات عامة
    total_orders = ProductionOrder.objects.count()
    active_orders = ProductionOrder.objects.filter(status__in=['confirmed', 'in_progress']).count()
    completed_orders = ProductionOrder.objects.filter(status='completed').count()
    pending_orders = ProductionOrder.objects.filter(status='draft').count()

    # إحصائيات هذا الشهر
    current_month = timezone.now().replace(day=1)
    monthly_orders = ProductionOrder.objects.filter(created_at__gte=current_month).count()
    monthly_completed = ProductionOrder.objects.filter(
        status='completed',
        actual_end_date__gte=current_month
    ).count()

    # أوامر الإنتاج الحديثة
    recent_orders = ProductionOrder.objects.select_related(
        'product', 'target_warehouse', 'created_by'
    ).order_by('-created_at')[:10]

    # أوامر الإنتاج العاجلة
    urgent_orders = ProductionOrder.objects.filter(
        priority='urgent',
        status__in=['confirmed', 'in_progress']
    ).select_related('product', 'target_warehouse')[:5]

    # أوامر الإنتاج المتأخرة
    overdue_orders = ProductionOrder.objects.filter(
        planned_end_date__lt=timezone.now(),
        status__in=['confirmed', 'in_progress']
    ).count()

    # إحصائيات التكلفة
    total_production_cost = ProductionOrder.objects.filter(
        status='completed'
    ).aggregate(total=Sum('total_cost'))['total'] or Decimal('0')

    # إحصائيات الكفاءة
    efficiency_data = ProductionReport.objects.aggregate(
        avg_efficiency=Avg('efficiency_percentage')
    )
    avg_efficiency = efficiency_data['avg_efficiency'] or 0

    context = {
        'total_orders': total_orders,
        'active_orders': active_orders,
        'completed_orders': completed_orders,
        'pending_orders': pending_orders,
        'monthly_orders': monthly_orders,
        'monthly_completed': monthly_completed,
        'recent_orders': recent_orders,
        'urgent_orders': urgent_orders,
        'overdue_orders': overdue_orders,
        'total_production_cost': total_production_cost,
        'avg_efficiency': avg_efficiency,
        'today': timezone.now().date(),
    }

    return render(request, 'manufacturing/dashboard.html', context)

@login_required
def production_order_create(request):
    """إنشاء أمر إنتاج جديد"""
    if request.method == 'POST':
        form = ProductionOrderForm(request.POST)

        if form.is_valid():
            # إنشاء أمر الإنتاج
            production_order = form.save(commit=False)
            production_order.created_by = request.user

            # توليد رقم أمر الإنتاج
            current_year = timezone.now().year
            last_order = ProductionOrder.objects.filter(
                order_number__startswith=f'PO{current_year}'
            ).order_by('-order_number').first()

            if last_order:
                last_number = int(last_order.order_number[-4:])
                new_number = last_number + 1
            else:
                new_number = 1

            production_order.order_number = f'PO{current_year}{new_number:04d}'
            production_order.save()

            messages.success(request, f'تم إنشاء أمر الإنتاج {production_order.order_number} بنجاح!')
            return redirect('manufacturing:production_order_detail', pk=production_order.pk)
    else:
        form = ProductionOrderForm()

    # الحصول على المواد الخام والمخازن
    raw_materials = Product.objects.filter(is_active=True)
    warehouses = Warehouse.objects.filter(is_active=True)

    context = {
        'form': form,
        'title': 'إنشاء أمر إنتاج جديد',
        'raw_materials': raw_materials,
        'warehouses': warehouses,
    }

    return render(request, 'manufacturing/production_order_form.html', context)

@login_required
def production_order_list(request):
    """قائمة أوامر الإنتاج"""
    orders = ProductionOrder.objects.select_related(
        'product', 'target_warehouse', 'created_by'
    ).order_by('-created_at')

    # التصفح
    paginator = Paginator(orders, 15)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'total_orders': orders.count()
    }

    return render(request, 'manufacturing/production_order_list.html', context)

@login_required
def production_order_detail(request, pk):
    """تفاصيل أمر الإنتاج"""
    order = get_object_or_404(ProductionOrder, pk=pk)

    context = {
        'order': order,
    }

    return render(request, 'manufacturing/production_order_detail.html', context)

@login_required
def get_material_info(request):
    """الحصول على معلومات المادة الخام (AJAX)"""
    return JsonResponse({'status': 'ok'})

@login_required
def calculate_production_cost(request):
    """حساب تكلفة الإنتاج (AJAX)"""
    return JsonResponse({'status': 'ok'})
