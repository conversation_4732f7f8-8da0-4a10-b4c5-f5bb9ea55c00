"""
URL configuration for osaric project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.contrib.auth import views as auth_views

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', include('dashboard.urls')),
    path('accounts/login/', auth_views.LoginView.as_view(), name='login'),
    path('customers/', include('customers.urls', namespace='customers')),
    path('products/', include('products.urls', namespace='products')),
    path('definitions/', include('definitions.urls', namespace='definitions')),
    path('banks/', include('banks.urls', namespace='banks')),
    path('assets/', include('assets.urls', namespace='assets')),
    path('branches/', include('branches.urls', namespace='branches')),
    path('chart-of-accounts/', include('accounts.urls', namespace='accounts')),
    path('hr/', include('hr.urls', namespace='hr')),
    path('sales/', include('sales.urls', namespace='sales')),
    path('purchases/', include('purchases.urls', namespace='purchases')),
    path('reports/', include('reports.urls', namespace='reports')),
]
