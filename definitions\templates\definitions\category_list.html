{% extends 'base.html' %}
{% load static %}

{% block title %}فئات الأصناف{% endblock %}

{% block extra_css %}
<style>
    .category-header {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        padding: 2rem 0;
        margin-bottom: 2rem;
        color: white;
        border-radius: 15px;
    }

    .category-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        overflow: hidden;
        margin-bottom: 1.5rem;
        border-left: 4px solid #11998e;
    }

    .category-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .category-card-body {
        padding: 1.5rem;
    }

    .category-header-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }

    .category-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 0.25rem;
    }

    .category-code {
        color: #718096;
        font-size: 0.9rem;
        font-weight: 500;
    }

    .category-path {
        background: #f7fafc;
        padding: 0.5rem 1rem;
        border-radius: 8px;
        font-size: 0.85rem;
        color: #4a5568;
        margin-bottom: 1rem;
        border-left: 3px solid #11998e;
    }

    .category-meta {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .meta-item {
        display: flex;
        align-items: center;
        color: #666;
        font-size: 0.9rem;
    }

    .meta-item i {
        margin-left: 0.5rem;
        color: #11998e;
        width: 16px;
    }

    .category-actions {
        display: flex;
        gap: 0.5rem;
        justify-content: flex-end;
    }

    .btn-action {
        padding: 0.4rem 0.8rem;
        border-radius: 6px;
        border: none;
        font-size: 0.8rem;
        font-weight: 500;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
    }

    .btn-view {
        background: #e3f2fd;
        color: #1976d2;
    }

    .btn-edit {
        background: #fff3e0;
        color: #f57c00;
    }

    .btn-delete {
        background: #ffebee;
        color: #d32f2f;
    }

    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .search-filters {
        background: white;
        padding: 1.5rem;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
    }

    .stats-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: white;
        padding: 1.5rem;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        text-align: center;
    }

    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: #11998e;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        color: #666;
        font-size: 0.9rem;
    }

    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .status-active {
        background: #d4edda;
        color: #155724;
    }

    .status-inactive {
        background: #f8d7da;
        color: #721c24;
    }

    .category-image {
        width: 60px;
        height: 60px;
        border-radius: 10px;
        object-fit: cover;
        margin-left: 1rem;
    }

    .no-image {
        width: 60px;
        height: 60px;
        border-radius: 10px;
        background: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #adb5bd;
        margin-left: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<!-- Category Header -->
<div class="category-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-tags me-2"></i>
                    فئات الأصناف
                </h1>
                <p class="mb-0 opacity-75">تصنيف وتنظيم الأصناف في مجموعات وفئات</p>
            </div>
            <div class="col-md-4 text-end">
                <a href="#" class="btn btn-light btn-lg">
                    <i class="bi bi-plus-circle me-2"></i>
                    إضافة فئة جديدة
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Statistics -->
<div class="container-fluid">
    <div class="stats-row">
        <div class="stat-card">
            <div class="stat-number">{{ total_categories }}</div>
            <div class="stat-label">إجمالي الفئات</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ page_obj.paginator.count }}</div>
            <div class="stat-label">الفئات المعروضة</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ page_obj.paginator.num_pages }}</div>
            <div class="stat-label">عدد الصفحات</div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="search-filters">
        <form method="get" class="row g-3">
            <div class="col-md-6">
                <label class="form-label">البحث</label>
                <input type="text" class="form-control" name="search" value="{{ search_query }}" 
                       placeholder="البحث بالاسم أو الكود...">
            </div>
            <div class="col-md-4 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="bi bi-search me-1"></i>بحث
                </button>
                <a href="{% url 'definitions:category_list' %}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-clockwise me-1"></i>إعادة تعيين
                </a>
            </div>
        </form>
    </div>

    <!-- Categories List -->
    <div class="row">
        {% for category in page_obj %}
        <div class="col-lg-6 col-xl-4">
            <div class="category-card">
                <div class="category-card-body">
                    <div class="category-header-info">
                        <div class="flex-grow-1">
                            <div class="category-title">{{ category.name }}</div>
                            <div class="category-code">{{ category.code }}</div>
                        </div>
                        <div class="d-flex align-items-center">
                            {% if category.image %}
                                <img src="{{ category.image.url }}" alt="{{ category.name }}" class="category-image">
                            {% else %}
                                <div class="no-image">
                                    <i class="bi bi-image" style="font-size: 1.5rem;"></i>
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    {% if category.parent %}
                    <div class="category-path">
                        <i class="bi bi-diagram-3 me-1"></i>
                        {{ category.get_full_path }}
                    </div>
                    {% endif %}

                    {% if category.description %}
                    <div class="mb-3">
                        <small class="text-muted">{{ category.description|truncatechars:100 }}</small>
                    </div>
                    {% endif %}

                    <div class="category-meta">
                        <div class="meta-item">
                            <i class="bi bi-calendar"></i>
                            <span>{{ category.created_at|date:"d/m/Y" }}</span>
                        </div>
                        <div class="meta-item">
                            <i class="bi bi-person"></i>
                            <span>{{ category.created_by.username }}</span>
                        </div>
                        <div class="meta-item">
                            <i class="bi bi-sort-numeric-up"></i>
                            <span>ترتيب: {{ category.sort_order }}</span>
                        </div>
                        <div class="meta-item">
                            <span class="status-badge {% if category.is_active %}status-active{% else %}status-inactive{% endif %}">
                                {{ category.is_active|yesno:"نشط,غير نشط" }}
                            </span>
                        </div>
                    </div>

                    <div class="category-actions">
                        <a href="#" class="btn-action btn-view">
                            <i class="bi bi-eye"></i>عرض
                        </a>
                        <a href="#" class="btn-action btn-edit">
                            <i class="bi bi-pencil"></i>تعديل
                        </a>
                        <a href="#" class="btn-action btn-delete" onclick="return confirm('هل أنت متأكد من حذف هذه الفئة؟')">
                            <i class="bi bi-trash"></i>حذف
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="bi bi-tags" style="font-size: 4rem; color: #ddd;"></i>
                <h4 class="mt-3 text-muted">لا توجد فئات</h4>
                <p class="text-muted">لم يتم العثور على أي فئات تطابق معايير البحث</p>
                <a href="#" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>إضافة فئة جديدة
                </a>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <div class="d-flex justify-content-center mt-4">
        <nav aria-label="تنقل الصفحات">
            <ul class="pagination">
                {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}">الأولى</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">السابقة</a>
                    </li>
                {% endif %}

                <li class="page-item active">
                    <span class="page-link">{{ page_obj.number }} من {{ page_obj.paginator.num_pages }}</span>
                </li>

                {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">التالية</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}">الأخيرة</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
    </div>
    {% endif %}
</div>
{% endblock %}
