from django.db import models
from django.core.validators import MinValueValidator
from definitions.models import Currency

class AssetCategory(models.Model):
    """تصنيفات الأصول الثابتة"""
    name = models.CharField(max_length=100, verbose_name="اسم التصنيف")
    code = models.CharField(max_length=20, unique=True, verbose_name="رمز التصنيف")
    depreciation_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0, verbose_name="معدل الاستهلاك السنوي %")
    useful_life_years = models.IntegerField(default=5, verbose_name="العمر الافتراضي (سنوات)")
    description = models.TextField(blank=True, verbose_name="الوصف")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "تصنيف أصل"
        verbose_name_plural = "تصنيفات الأصول"
        ordering = ['name']

    def __str__(self):
        return self.name

class Asset(models.Model):
    """الأصول الثابتة"""
    STATUS_CHOICES = [
        ('active', 'نشط'),
        ('maintenance', 'تحت الصيانة'),
        ('disposed', 'تم التخلص منه'),
        ('sold', 'تم البيع'),
    ]

    name = models.CharField(max_length=200, verbose_name="اسم الأصل")
    code = models.CharField(max_length=50, unique=True, verbose_name="رمز الأصل")
    category = models.ForeignKey(AssetCategory, on_delete=models.CASCADE, verbose_name="التصنيف")
    description = models.TextField(blank=True, verbose_name="الوصف")

    # Financial Information
    purchase_price = models.DecimalField(max_digits=15, decimal_places=2, verbose_name="سعر الشراء")
    currency = models.ForeignKey(Currency, on_delete=models.CASCADE, verbose_name="العملة")
    purchase_date = models.DateField(verbose_name="تاريخ الشراء")
    useful_life_years = models.IntegerField(verbose_name="العمر الافتراضي (سنوات)")
    salvage_value = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="القيمة المتبقية")

    # Current Status
    current_value = models.DecimalField(max_digits=15, decimal_places=2, verbose_name="القيمة الحالية")
    accumulated_depreciation = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="مجمع الاستهلاك")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active', verbose_name="الحالة")

    # Location and Details
    location = models.CharField(max_length=200, blank=True, verbose_name="الموقع")
    serial_number = models.CharField(max_length=100, blank=True, verbose_name="الرقم التسلسلي")
    manufacturer = models.CharField(max_length=100, blank=True, verbose_name="الشركة المصنعة")
    model = models.CharField(max_length=100, blank=True, verbose_name="الموديل")

    # Maintenance
    last_maintenance_date = models.DateField(null=True, blank=True, verbose_name="تاريخ آخر صيانة")
    next_maintenance_date = models.DateField(null=True, blank=True, verbose_name="تاريخ الصيانة القادمة")

    # Tracking
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "أصل ثابت"
        verbose_name_plural = "الأصول الثابتة"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} ({self.code})"

    @property
    def book_value(self):
        """القيمة الدفترية = سعر الشراء - مجمع الاستهلاك"""
        return self.purchase_price - self.accumulated_depreciation

    @property
    def annual_depreciation(self):
        """الاستهلاك السنوي"""
        if self.useful_life_years > 0:
            return (self.purchase_price - self.salvage_value) / self.useful_life_years
        return 0

class AssetMaintenance(models.Model):
    """صيانة الأصول"""
    MAINTENANCE_TYPES = [
        ('preventive', 'صيانة وقائية'),
        ('corrective', 'صيانة إصلاحية'),
        ('emergency', 'صيانة طارئة'),
    ]

    asset = models.ForeignKey(Asset, on_delete=models.CASCADE, verbose_name="الأصل")
    maintenance_type = models.CharField(max_length=20, choices=MAINTENANCE_TYPES, verbose_name="نوع الصيانة")
    description = models.TextField(verbose_name="وصف الصيانة")
    maintenance_date = models.DateField(verbose_name="تاريخ الصيانة")
    cost = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="التكلفة")
    vendor = models.CharField(max_length=200, blank=True, verbose_name="مقدم الخدمة")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "صيانة أصل"
        verbose_name_plural = "صيانة الأصول"
        ordering = ['-maintenance_date']

    def __str__(self):
        return f"{self.asset.name} - {self.get_maintenance_type_display()}"
