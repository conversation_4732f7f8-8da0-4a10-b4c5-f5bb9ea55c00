from django.shortcuts import render, redirect
from django.contrib.auth.models import User
from .forms import SignUpForm

def signup_view(request):
    if request.method == 'POST':
        form = SignUpForm(request.POST)
        if form.is_valid():
            user = form.save()
            # Log the user in
            # auth_login(request, user)
            return redirect('login')  # Redirect to login page
    else:
        form = SignUpForm()
    return render(request, 'registration/signup.html', {'form': form})
