# Generated by Django 5.2.4 on 2025-07-11 04:26

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Currency',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم العملة')),
                ('code', models.CharField(max_length=10, unique=True, verbose_name='رمز العملة')),
                ('symbol', models.CharField(max_length=10, verbose_name='رمز العملة')),
                ('exchange_rate', models.DecimalField(decimal_places=4, default=1.0, max_digits=10, verbose_name='سعر الصرف')),
                ('is_base', models.BooleanField(default=False, verbose_name='العملة الأساسية')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'عملة',
                'verbose_name_plural': 'العملات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='PaymentMethod',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم طريقة الدفع')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='رمز طريقة الدفع')),
                ('payment_type', models.CharField(choices=[('cash', 'نقدي'), ('bank', 'بنكي'), ('credit', 'ائتمان'), ('check', 'شيك'), ('online', 'إلكتروني')], max_length=20, verbose_name='نوع الدفع')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'طريقة دفع',
                'verbose_name_plural': 'طرق الدفع',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='TaxRate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الضريبة')),
                ('rate', models.DecimalField(decimal_places=2, max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='المعدل %')),
                ('tax_type', models.CharField(choices=[('vat', 'ضريبة القيمة المضافة'), ('sales', 'ضريبة المبيعات'), ('income', 'ضريبة الدخل'), ('other', 'أخرى')], max_length=20, verbose_name='نوع الضريبة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'معدل ضريبة',
                'verbose_name_plural': 'معدلات الضرائب',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم التصنيف')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='رمز التصنيف')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='definitions.category', verbose_name='التصنيف الأب')),
            ],
            options={
                'verbose_name': 'تصنيف',
                'verbose_name_plural': 'التصنيفات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Unit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الوحدة')),
                ('symbol', models.CharField(max_length=20, verbose_name='رمز الوحدة')),
                ('unit_type', models.CharField(choices=[('weight', 'وزن'), ('length', 'طول'), ('volume', 'حجم'), ('area', 'مساحة'), ('quantity', 'كمية'), ('time', 'وقت'), ('other', 'أخرى')], max_length=20, verbose_name='نوع الوحدة')),
                ('conversion_factor', models.DecimalField(decimal_places=4, default=1.0, max_digits=10, verbose_name='معامل التحويل')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('base_unit', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='definitions.unit', verbose_name='الوحدة الأساسية')),
            ],
            options={
                'verbose_name': 'وحدة',
                'verbose_name_plural': 'الوحدات',
                'ordering': ['name'],
            },
        ),
    ]
