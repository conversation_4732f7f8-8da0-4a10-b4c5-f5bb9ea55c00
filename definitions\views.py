from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q
from .models import Currency, Unit, Category, PaymentMethod, TaxRate
from .forms import CurrencyForm, UnitForm, CategoryForm, PaymentMethodForm, TaxRateForm

@login_required
def definitions_dashboard(request):
    """لوحة تحكم التعريفات"""
    context = {
        'currencies_count': Currency.objects.filter(is_active=True).count(),
        'units_count': Unit.objects.filter(is_active=True).count(),
        'categories_count': Category.objects.filter(is_active=True).count(),
        'payment_methods_count': PaymentMethod.objects.filter(is_active=True).count(),
        'tax_rates_count': TaxRate.objects.filter(is_active=True).count(),
    }
    return render(request, 'definitions/dashboard.html', context)

# Currency Views
@login_required
def currency_list(request):
    search_query = request.GET.get('search', '')
    currencies = Currency.objects.all()

    if search_query:
        currencies = currencies.filter(
            Q(name__icontains=search_query) |
            Q(code__icontains=search_query)
        )

    currencies = currencies.order_by('-created_at')

    paginator = Paginator(currencies, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'total_currencies': Currency.objects.count()
    }
    return render(request, 'definitions/currency_list.html', context)

@login_required
def currency_create(request):
    if request.method == 'POST':
        form = CurrencyForm(request.POST)
        if form.is_valid():
            currency = form.save()
            messages.success(request, f'تم إضافة العملة "{currency.name}" بنجاح!')
            return redirect('definitions:currency_list')
    else:
        form = CurrencyForm()

    return render(request, 'definitions/currency_form.html', {
        'form': form,
        'title': 'إضافة عملة جديدة',
        'button_text': 'إضافة العملة'
    })

@login_required
def currency_edit(request, pk):
    currency = get_object_or_404(Currency, pk=pk)

    if request.method == 'POST':
        form = CurrencyForm(request.POST, instance=currency)
        if form.is_valid():
            currency = form.save()
            messages.success(request, f'تم تحديث العملة "{currency.name}" بنجاح!')
            return redirect('definitions:currency_list')
    else:
        form = CurrencyForm(instance=currency)

    return render(request, 'definitions/currency_form.html', {
        'form': form,
        'currency': currency,
        'title': f'تعديل العملة: {currency.name}',
        'button_text': 'حفظ التغييرات'
    })

@login_required
def currency_delete(request, pk):
    currency = get_object_or_404(Currency, pk=pk)

    if request.method == 'POST':
        currency_name = currency.name
        currency.delete()
        messages.success(request, f'تم حذف العملة "{currency_name}" بنجاح!')
        return redirect('definitions:currency_list')

    return render(request, 'definitions/currency_confirm_delete.html', {'currency': currency})

# Unit Views
@login_required
def unit_list(request):
    search_query = request.GET.get('search', '')
    units = Unit.objects.all()

    if search_query:
        units = units.filter(
            Q(name__icontains=search_query) |
            Q(symbol__icontains=search_query)
        )

    units = units.order_by('-created_at')

    paginator = Paginator(units, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'total_units': Unit.objects.count()
    }
    return render(request, 'definitions/unit_list.html', context)

@login_required
def unit_create(request):
    if request.method == 'POST':
        form = UnitForm(request.POST)
        if form.is_valid():
            unit = form.save()
            messages.success(request, f'تم إضافة الوحدة "{unit.name}" بنجاح!')
            return redirect('definitions:unit_list')
    else:
        form = UnitForm()

    return render(request, 'definitions/unit_form.html', {
        'form': form,
        'title': 'إضافة وحدة جديدة',
        'button_text': 'إضافة الوحدة'
    })

@login_required
def unit_edit(request, pk):
    unit = get_object_or_404(Unit, pk=pk)

    if request.method == 'POST':
        form = UnitForm(request.POST, instance=unit)
        if form.is_valid():
            unit = form.save()
            messages.success(request, f'تم تحديث الوحدة "{unit.name}" بنجاح!')
            return redirect('definitions:unit_list')
    else:
        form = UnitForm(instance=unit)

    return render(request, 'definitions/unit_form.html', {
        'form': form,
        'unit': unit,
        'title': f'تعديل الوحدة: {unit.name}',
        'button_text': 'حفظ التغييرات'
    })

@login_required
def unit_delete(request, pk):
    unit = get_object_or_404(Unit, pk=pk)

    if request.method == 'POST':
        unit_name = unit.name
        unit.delete()
        messages.success(request, f'تم حذف الوحدة "{unit_name}" بنجاح!')
        return redirect('definitions:unit_list')

    return render(request, 'definitions/unit_confirm_delete.html', {'unit': unit})
