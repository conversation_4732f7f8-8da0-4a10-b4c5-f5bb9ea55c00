from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Q, Count
from django.core.paginator import Paginator
from django.http import JsonResponse
from .models import (
    WarehouseDefinition, ProductDefinition, ProductCategory,
    CurrencyDefinition, BankDefinition, CashBoxDefinition
)

@login_required
def definitions_dashboard(request):
    """لوحة تحكم التعريفات"""
    # إحصائيات التعريفات
    stats = {
        'warehouses': WarehouseDefinition.objects.count(),
        'products': ProductDefinition.objects.count(),
        'categories': ProductCategory.objects.count(),
        'currencies': CurrencyDefinition.objects.count(),
        'banks': BankDefinition.objects.count(),
        'cashboxes': CashBoxDefinition.objects.count(),
    }

    context = {
        'stats': stats,
    }

    return render(request, 'definitions/dashboard.html', context)

@login_required
def warehouse_list(request):
    """قائمة تعريف المخازن"""
    search_query = request.GET.get('search', '')
    warehouse_type = request.GET.get('type', '')

    warehouses = WarehouseDefinition.objects.all()

    if search_query:
        warehouses = warehouses.filter(
            Q(name__icontains=search_query) |
            Q(code__icontains=search_query) |
            Q(manager_name__icontains=search_query) |
            Q(address__icontains=search_query)
        )

    if warehouse_type:
        warehouses = warehouses.filter(warehouse_type=warehouse_type)

    warehouses = warehouses.order_by('-created_at')

    # Pagination
    paginator = Paginator(warehouses, 12)  # 12 مخازن في الصفحة
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # إحصائيات
    total_warehouses = WarehouseDefinition.objects.count()

    # أنواع المخازن للفلترة
    warehouse_types = WarehouseDefinition.WAREHOUSE_TYPES

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'warehouse_type': warehouse_type,
        'warehouse_types': warehouse_types,
        'total_warehouses': total_warehouses,
    }

    return render(request, 'definitions/warehouse_list.html', context)

@login_required
def warehouse_create(request):
    """إنشاء مخزن جديد"""
    if request.method == 'POST':
        # استخراج البيانات من النموذج
        code = request.POST.get('code')
        name = request.POST.get('name')
        warehouse_type = request.POST.get('warehouse_type')
        address = request.POST.get('address', '')
        phone = request.POST.get('phone', '')
        manager_name = request.POST.get('manager_name', '')
        description = request.POST.get('description', '')
        is_active = request.POST.get('is_active') == 'on'
        allow_negative_stock = request.POST.get('allow_negative_stock') == 'on'
        auto_reorder = request.POST.get('auto_reorder') == 'on'

        # التحقق من عدم تكرار الكود
        if WarehouseDefinition.objects.filter(code=code).exists():
            messages.error(request, f'كود المخزن "{code}" موجود بالفعل. يرجى استخدام كود آخر.')
            return render(request, 'definitions/warehouse_form.html', {
                'warehouse_types': WarehouseDefinition.WAREHOUSE_TYPES,
                'form_data': request.POST
            })

        # إنشاء المخزن
        warehouse = WarehouseDefinition.objects.create(
            code=code,
            name=name,
            warehouse_type=warehouse_type,
            address=address,
            phone=phone,
            manager_name=manager_name,
            description=description,
            is_active=is_active,
            allow_negative_stock=allow_negative_stock,
            auto_reorder=auto_reorder,
            created_by=request.user
        )

        messages.success(request, f'تم إنشاء المخزن "{warehouse.name}" بنجاح!')
        return redirect('definitions:warehouse_list')

    context = {
        'warehouse_types': WarehouseDefinition.WAREHOUSE_TYPES,
        'action': 'create'
    }

    return render(request, 'definitions/warehouse_form.html', context)

@login_required
def warehouse_edit(request, warehouse_id):
    """تعديل مخزن موجود"""
    warehouse = get_object_or_404(WarehouseDefinition, id=warehouse_id)

    if request.method == 'POST':
        # استخراج البيانات من النموذج
        code = request.POST.get('code')
        name = request.POST.get('name')
        warehouse_type = request.POST.get('warehouse_type')
        address = request.POST.get('address', '')
        phone = request.POST.get('phone', '')
        manager_name = request.POST.get('manager_name', '')
        description = request.POST.get('description', '')
        is_active = request.POST.get('is_active') == 'on'
        allow_negative_stock = request.POST.get('allow_negative_stock') == 'on'
        auto_reorder = request.POST.get('auto_reorder') == 'on'

        # التحقق من عدم تكرار الكود (باستثناء المخزن الحالي)
        if WarehouseDefinition.objects.filter(code=code).exclude(id=warehouse.id).exists():
            messages.error(request, f'كود المخزن "{code}" موجود بالفعل. يرجى استخدام كود آخر.')
            return render(request, 'definitions/warehouse_form.html', {
                'warehouse': warehouse,
                'warehouse_types': WarehouseDefinition.WAREHOUSE_TYPES,
                'action': 'edit',
                'form_data': request.POST
            })

        # تحديث المخزن
        warehouse.code = code
        warehouse.name = name
        warehouse.warehouse_type = warehouse_type
        warehouse.address = address
        warehouse.phone = phone
        warehouse.manager_name = manager_name
        warehouse.description = description
        warehouse.is_active = is_active
        warehouse.allow_negative_stock = allow_negative_stock
        warehouse.auto_reorder = auto_reorder
        warehouse.save()

        messages.success(request, f'تم تحديث المخزن "{warehouse.name}" بنجاح!')
        return redirect('definitions:warehouse_list')

    context = {
        'warehouse': warehouse,
        'warehouse_types': WarehouseDefinition.WAREHOUSE_TYPES,
        'action': 'edit'
    }

    return render(request, 'definitions/warehouse_form.html', context)

@login_required
def warehouse_delete(request, warehouse_id):
    """حذف مخزن"""
    warehouse = get_object_or_404(WarehouseDefinition, id=warehouse_id)

    if request.method == 'POST':
        warehouse_name = warehouse.name
        warehouse.delete()
        messages.success(request, f'تم حذف المخزن "{warehouse_name}" بنجاح!')
        return redirect('definitions:warehouse_list')

    context = {
        'warehouse': warehouse,
    }

    return render(request, 'definitions/warehouse_confirm_delete.html', context)

@login_required
def warehouse_detail(request, warehouse_id):
    """تفاصيل المخزن"""
    warehouse = get_object_or_404(WarehouseDefinition, id=warehouse_id)

    context = {
        'warehouse': warehouse,
    }

    return render(request, 'definitions/warehouse_detail.html', context)
