from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Count
from django.utils import timezone
from django.http import JsonResponse

from .models import (
    WarehouseDefinition, ProductCategory, ProductDefinition, ProductLocation,
    ProductCode, BankDefinition, CurrencyDefinition, CashBoxDefinition,
    AssetGroup, PersonDefinition, ExpenseType, ExpenseName,
    RevenueType, RevenueName, ProfitCenter, PrinterDefinition
)

@login_required
def definitions_dashboard(request):
    """لوحة تحكم التعريفات"""

    # إحصائيات عامة
    stats = {
        'warehouses': WarehouseDefinition.objects.filter(is_active=True).count(),
        'products': ProductDefinition.objects.filter(is_active=True).count(),
        'categories': ProductCategory.objects.filter(is_active=True).count(),
        'persons': PersonDefinition.objects.filter(is_active=True).count(),
        'banks': BankDefinition.objects.filter(is_active=True).count(),
        'currencies': CurrencyDefinition.objects.filter(is_active=True).count(),
        'cashboxes': CashBoxDefinition.objects.filter(is_active=True).count(),
        'asset_groups': AssetGroup.objects.filter(is_active=True).count(),
        'expense_types': ExpenseType.objects.filter(is_active=True).count(),
        'revenue_types': RevenueType.objects.filter(is_active=True).count(),
        'profit_centers': ProfitCenter.objects.filter(is_active=True).count(),
        'printers': PrinterDefinition.objects.filter(is_active=True).count(),
    }

    # آخر التحديثات
    recent_updates = []

    # آخر المخازن المضافة
    recent_warehouses = WarehouseDefinition.objects.order_by('-created_at')[:5]
    for warehouse in recent_warehouses:
        recent_updates.append({
            'type': 'warehouse',
            'name': warehouse.name,
            'date': warehouse.created_at,
            'icon': 'bi-building',
            'color': 'primary'
        })

    # آخر المنتجات المضافة
    recent_products = ProductDefinition.objects.order_by('-created_at')[:5]
    for product in recent_products:
        recent_updates.append({
            'type': 'product',
            'name': product.name,
            'date': product.created_at,
            'icon': 'bi-box',
            'color': 'success'
        })

    # ترتيب حسب التاريخ
    recent_updates.sort(key=lambda x: x['date'], reverse=True)
    recent_updates = recent_updates[:10]

    context = {
        'stats': stats,
        'recent_updates': recent_updates,
        'today': timezone.now().date(),
    }

    return render(request, 'definitions/dashboard.html', context)

# ===== تعريف المخازن =====
@login_required
def warehouse_list(request):
    """قائمة المخازن"""
    search_query = request.GET.get('search', '')
    warehouse_type = request.GET.get('type', '')

    warehouses = WarehouseDefinition.objects.all()

    if search_query:
        warehouses = warehouses.filter(
            Q(name__icontains=search_query) |
            Q(code__icontains=search_query) |
            Q(manager_name__icontains=search_query)
        )

    if warehouse_type:
        warehouses = warehouses.filter(warehouse_type=warehouse_type)

    warehouses = warehouses.order_by('code')

    paginator = Paginator(warehouses, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'warehouse_type': warehouse_type,
        'warehouse_types': WarehouseDefinition.WAREHOUSE_TYPES,
        'total_warehouses': warehouses.count()
    }

    return render(request, 'definitions/warehouse_list.html', context)

# ===== فئات الأصناف =====
@login_required
def category_list(request):
    """قائمة فئات الأصناف"""
    search_query = request.GET.get('search', '')

    categories = ProductCategory.objects.select_related('parent')

    if search_query:
        categories = categories.filter(
            Q(name__icontains=search_query) |
            Q(code__icontains=search_query)
        )

    categories = categories.order_by('code')

    paginator = Paginator(categories, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'total_categories': categories.count()
    }

    return render(request, 'definitions/category_list.html', context)

# ===== تعريف الأصناف =====
@login_required
def product_list(request):
    """قائمة الأصناف"""
    search_query = request.GET.get('search', '')
    category_filter = request.GET.get('category', '')
    product_type = request.GET.get('type', '')

    products = ProductDefinition.objects.select_related('category')

    if search_query:
        products = products.filter(
            Q(name__icontains=search_query) |
            Q(code__icontains=search_query) |
            Q(barcode__icontains=search_query)
        )

    if category_filter:
        products = products.filter(category_id=category_filter)

    if product_type:
        products = products.filter(product_type=product_type)

    products = products.order_by('code')

    paginator = Paginator(products, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    categories = ProductCategory.objects.filter(is_active=True)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'category_filter': category_filter,
        'product_type': product_type,
        'categories': categories,
        'product_types': ProductDefinition.PRODUCT_TYPES,
        'total_products': products.count()
    }

    return render(request, 'definitions/product_list.html', context)
