{% extends 'base.html' %}

{% block title %}{{ title }} - أوساريك{% endblock %}

{% block content %}
    <div class="page-header">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'dashboard_home' %}">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="{% url 'definitions:dashboard' %}">التعريفات</a></li>
                <li class="breadcrumb-item"><a href="{% url 'definitions:currency_list' %}">العملات</a></li>
                <li class="breadcrumb-item active">{{ title }}</li>
            </ol>
        </nav>
        <h1 class="page-title">{{ title }}</h1>
        <p class="page-subtitle">
            {% if currency %}
                تعديل بيانات العملة في النظام
            {% else %}
                إضافة عملة جديدة إلى النظام
            {% endif %}
        </p>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-currency-exchange me-2"></i>بيانات العملة
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.name.id_for_label }}" class="form-label">
                                    <i class="bi bi-tag me-1"></i>{{ form.name.label }}
                                    <span class="text-danger">*</span>
                                </label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.name.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <label for="{{ form.code.id_for_label }}" class="form-label">
                                    <i class="bi bi-code me-1"></i>{{ form.code.label }}
                                    <span class="text-danger">*</span>
                                </label>
                                {{ form.code }}
                                {% if form.code.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.code.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">مثال: USD, EUR, EGP</div>
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <label for="{{ form.symbol.id_for_label }}" class="form-label">
                                    <i class="bi bi-currency-dollar me-1"></i>{{ form.symbol.label }}
                                    <span class="text-danger">*</span>
                                </label>
                                {{ form.symbol }}
                                {% if form.symbol.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.symbol.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">مثال: $, €, ج.م</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.exchange_rate.id_for_label }}" class="form-label">
                                    <i class="bi bi-arrow-left-right me-1"></i>{{ form.exchange_rate.label }}
                                    <span class="text-danger">*</span>
                                </label>
                                {{ form.exchange_rate }}
                                {% if form.exchange_rate.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.exchange_rate.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">سعر الصرف مقابل العملة الأساسية</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الإعدادات</label>
                                <div class="form-check">
                                    {{ form.is_base }}
                                    <label class="form-check-label" for="{{ form.is_base.id_for_label }}">
                                        {{ form.is_base.label }}
                                    </label>
                                    {% if form.is_base.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.is_base.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="form-check">
                                    {{ form.is_active }}
                                    <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                        {{ form.is_active.label }}
                                    </label>
                                    {% if form.is_active.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.is_active.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'definitions:currency_list' %}" class="btn btn-secondary">
                                <i class="bi bi-arrow-right me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-lg me-2"></i>{{ button_text }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            {% if currency %}
                <!-- Currency Information -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-info-circle me-2"></i>معلومات العملة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <div class="border rounded p-3">
                                    <i class="bi bi-currency-exchange text-primary" style="font-size: 2rem;"></i>
                                    <h4 class="mt-2 mb-1">{{ currency.code }}</h4>
                                    <p class="text-muted mb-0">رمز العملة</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="border rounded p-3">
                                    <i class="bi bi-currency-dollar text-success" style="font-size: 2rem;"></i>
                                    <h4 class="mt-2 mb-1">{{ currency.symbol }}</h4>
                                    <p class="text-muted mb-0">رمز العملة</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="border rounded p-3">
                                    <i class="bi bi-arrow-left-right text-info" style="font-size: 2rem;"></i>
                                    <h4 class="mt-2 mb-1">{{ currency.exchange_rate }}</h4>
                                    <p class="text-muted mb-0">سعر الصرف</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="border rounded p-3">
                                    <i class="bi bi-calendar-event text-warning" style="font-size: 2rem;"></i>
                                    <h4 class="mt-2 mb-1">{{ currency.created_at|date:"Y/m/d" }}</h4>
                                    <p class="text-muted mb-0">تاريخ الإضافة</p>
                                </div>
                            </div>
                        </div>
                        
                        {% if currency.is_base %}
                            <div class="alert alert-info mt-3">
                                <i class="bi bi-info-circle me-2"></i>
                                <strong>ملاحظة:</strong> هذه هي العملة الأساسية للنظام.
                            </div>
                        {% endif %}
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
{% endblock %}

{% block extra_css %}
<style>
    .form-label {
        font-weight: 600;
        color: #495057;
    }
    
    .form-control:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    }
    
    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
    }
    
    .breadcrumb {
        background-color: transparent;
        padding: 0;
        margin-bottom: 1rem;
    }
    
    .breadcrumb-item + .breadcrumb-item::before {
        content: "←";
    }
    
    .form-check {
        margin-bottom: 0.5rem;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // Form validation and enhancements
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.querySelector('form');
        const codeInput = document.getElementById('{{ form.code.id_for_label }}');
        const symbolInput = document.getElementById('{{ form.symbol.id_for_label }}');
        const isBaseCheckbox = document.getElementById('{{ form.is_base.id_for_label }}');
        
        // Auto-uppercase currency code
        codeInput.addEventListener('input', function() {
            this.value = this.value.toUpperCase();
        });
        
        // Base currency warning
        isBaseCheckbox.addEventListener('change', function() {
            if (this.checked) {
                if (!confirm('هل أنت متأكد من جعل هذه العملة الأساسية؟ سيتم إلغاء تفعيل العملة الأساسية الحالية.')) {
                    this.checked = false;
                }
            }
        });
        
        // Form submission
        form.addEventListener('submit', function() {
            const submitBtn = form.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>جاري الحفظ...';
            submitBtn.disabled = true;
        });
    });
</script>
{% endblock %}
