from django.urls import path
from . import views

app_name = 'warehouses'

urlpatterns = [
    # Dashboard
    path('', views.warehouses_dashboard, name='dashboard'),
    
    # Warehouse URLs
    path('warehouses/', views.warehouse_list, name='warehouse_list'),
    path('warehouses/create/', views.warehouse_create, name='warehouse_create'),
    path('warehouses/<int:pk>/', views.warehouse_detail, name='warehouse_detail'),
    path('warehouses/<int:pk>/edit/', views.warehouse_edit, name='warehouse_edit'),
    path('warehouses/<int:pk>/delete/', views.warehouse_delete, name='warehouse_delete'),
    
    # Stock URLs
    path('stock/', views.stock_list, name='stock_list'),
    path('stock/movements/', views.stock_movements, name='stock_movements'),
    path('stock/transfers/', views.stock_transfers, name='stock_transfers'),
    
    # Additional URLs will be added later
]
