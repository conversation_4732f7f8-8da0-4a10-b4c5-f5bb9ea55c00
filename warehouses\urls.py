from django.urls import path
from . import views

app_name = 'warehouses'

urlpatterns = [
    # لوحة التحكم الرئيسية
    path('', views.warehouses_dashboard, name='dashboard'),

    # إدارة المخزون
    path('inventory/', views.inventory_list, name='inventory_list'),
    path('add-stock/', views.add_stock, name='add_stock'),
    path('remove-stock/', views.remove_stock, name='remove_stock'),

    # تقارير وحركات
    path('transactions/', views.transactions_list, name='transactions_list'),
    path('low-stock/', views.low_stock_report, name='low_stock_report'),
    path('warehouse-report/<int:warehouse_id>/', views.warehouse_report, name='warehouse_report'),

    # تسويات المخزون
    path('adjustments/', views.adjustments_list, name='adjustments_list'),
    path('adjustments/create/', views.create_adjustment, name='create_adjustment'),
    path('adjustments/<int:adjustment_id>/', views.adjustment_detail, name='adjustment_detail'),

    # نقل بين المخازن
    path('transfer/', views.transfer_stock, name='transfer_stock'),

    # API endpoints
    path('api/product-balance/', views.get_product_balance, name='get_product_balance'),
    path('api/warehouse-products/', views.get_warehouse_products, name='get_warehouse_products'),
]
