from django import forms
from .models import Currency, Unit, Category, PaymentMethod, TaxRate

class CurrencyForm(forms.ModelForm):
    class Meta:
        model = Currency
        fields = ['name', 'code', 'symbol', 'exchange_rate', 'is_base', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم العملة'}),
            'code': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رمز العملة (مثل: USD)'}),
            'symbol': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رمز العملة (مثل: $)'}),
            'exchange_rate': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.0001', 'min': '0'}),
            'is_base': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
        labels = {
            'name': 'اسم العملة',
            'code': 'رمز العملة',
            'symbol': 'رمز العملة',
            'exchange_rate': 'سعر الصرف',
            'is_base': 'العملة الأساسية',
            'is_active': 'نشط',
        }

class UnitForm(forms.ModelForm):
    class Meta:
        model = Unit
        fields = ['name', 'symbol', 'unit_type', 'base_unit', 'conversion_factor', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم الوحدة'}),
            'symbol': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رمز الوحدة'}),
            'unit_type': forms.Select(attrs={'class': 'form-select'}),
            'base_unit': forms.Select(attrs={'class': 'form-select'}),
            'conversion_factor': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.0001', 'min': '0'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
        labels = {
            'name': 'اسم الوحدة',
            'symbol': 'رمز الوحدة',
            'unit_type': 'نوع الوحدة',
            'base_unit': 'الوحدة الأساسية',
            'conversion_factor': 'معامل التحويل',
            'is_active': 'نشط',
        }

class CategoryForm(forms.ModelForm):
    class Meta:
        model = Category
        fields = ['name', 'code', 'description', 'parent', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم التصنيف'}),
            'code': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رمز التصنيف'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'وصف التصنيف'}),
            'parent': forms.Select(attrs={'class': 'form-select'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
        labels = {
            'name': 'اسم التصنيف',
            'code': 'رمز التصنيف',
            'description': 'الوصف',
            'parent': 'التصنيف الأب',
            'is_active': 'نشط',
        }

class PaymentMethodForm(forms.ModelForm):
    class Meta:
        model = PaymentMethod
        fields = ['name', 'code', 'payment_type', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم طريقة الدفع'}),
            'code': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رمز طريقة الدفع'}),
            'payment_type': forms.Select(attrs={'class': 'form-select'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
        labels = {
            'name': 'اسم طريقة الدفع',
            'code': 'رمز طريقة الدفع',
            'payment_type': 'نوع الدفع',
            'is_active': 'نشط',
        }

class TaxRateForm(forms.ModelForm):
    class Meta:
        model = TaxRate
        fields = ['name', 'rate', 'tax_type', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم الضريبة'}),
            'rate': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0', 'max': '100'}),
            'tax_type': forms.Select(attrs={'class': 'form-select'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
        labels = {
            'name': 'اسم الضريبة',
            'rate': 'المعدل (%)',
            'tax_type': 'نوع الضريبة',
            'is_active': 'نشط',
        }
