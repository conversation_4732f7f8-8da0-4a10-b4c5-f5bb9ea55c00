from django import forms
from django.forms import inlineformset_factory
from .models import ProductionOrder, ProductionOrderMaterial, ProductionOperation, QualityCheck
from products.models import Product
from warehouses.models import Warehouse, Stock

class ProductionOrderForm(forms.ModelForm):
    class Meta:
        model = ProductionOrder
        fields = [
            'product', 'quantity', 'target_warehouse', 'priority',
            'planned_start_date', 'planned_end_date', 'notes'
        ]
        widgets = {
            'product': forms.Select(attrs={
                'class': 'form-select',
                'required': True
            }),
            'quantity': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0.01',
                'required': True
            }),
            'target_warehouse': forms.Select(attrs={
                'class': 'form-select',
                'required': True
            }),
            'priority': forms.Select(attrs={
                'class': 'form-select'
            }),
            'planned_start_date': forms.DateTimeInput(attrs={
                'class': 'form-control',
                'type': 'datetime-local',
                'required': True
            }),
            'planned_end_date': forms.DateTimeInput(attrs={
                'class': 'form-control',
                'type': 'datetime-local',
                'required': True
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'ملاحظات إضافية...'
            })
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # تخصيص الخيارات
        self.fields['product'].queryset = Product.objects.all()
        self.fields['target_warehouse'].queryset = Warehouse.objects.all()
        
        # إضافة تسميات
        self.fields['product'].label = "المنتج المراد تصنيعه"
        self.fields['quantity'].label = "الكمية المطلوبة"
        self.fields['target_warehouse'].label = "مخزن الإنتاج النهائي"
        self.fields['priority'].label = "الأولوية"
        self.fields['planned_start_date'].label = "تاريخ البدء المخطط"
        self.fields['planned_end_date'].label = "تاريخ الانتهاء المخطط"
        self.fields['notes'].label = "ملاحظات"

class ProductionOrderMaterialForm(forms.ModelForm):
    class Meta:
        model = ProductionOrderMaterial
        fields = ['material', 'warehouse', 'required_quantity', 'unit_cost']
        widgets = {
            'material': forms.Select(attrs={
                'class': 'form-select material-select',
                'required': True
            }),
            'warehouse': forms.Select(attrs={
                'class': 'form-select warehouse-select',
                'required': True
            }),
            'required_quantity': forms.NumberInput(attrs={
                'class': 'form-control quantity-input',
                'step': '0.01',
                'min': '0.01',
                'required': True
            }),
            'unit_cost': forms.NumberInput(attrs={
                'class': 'form-control unit-cost-input',
                'step': '0.01',
                'min': '0',
                'required': True
            })
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # تخصيص الخيارات
        self.fields['material'].queryset = Product.objects.all()
        self.fields['warehouse'].queryset = Warehouse.objects.all()
        
        # إضافة تسميات
        self.fields['material'].label = "المادة الخام"
        self.fields['warehouse'].label = "المخزن"
        self.fields['required_quantity'].label = "الكمية المطلوبة"
        self.fields['unit_cost'].label = "تكلفة الوحدة"

class ProductionOperationForm(forms.ModelForm):
    class Meta:
        model = ProductionOperation
        fields = [
            'operation_name', 'description', 'estimated_hours', 
            'hourly_rate', 'sequence', 'assigned_to'
        ]
        widgets = {
            'operation_name': forms.TextInput(attrs={
                'class': 'form-control',
                'required': True,
                'placeholder': 'اسم العملية'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 2,
                'placeholder': 'وصف العملية'
            }),
            'estimated_hours': forms.NumberInput(attrs={
                'class': 'form-control hours-input',
                'step': '0.25',
                'min': '0',
                'required': True
            }),
            'hourly_rate': forms.NumberInput(attrs={
                'class': 'form-control rate-input',
                'step': '0.01',
                'min': '0',
                'required': True
            }),
            'sequence': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1',
                'required': True
            }),
            'assigned_to': forms.Select(attrs={
                'class': 'form-select'
            })
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # إضافة تسميات
        self.fields['operation_name'].label = "اسم العملية"
        self.fields['description'].label = "وصف العملية"
        self.fields['estimated_hours'].label = "الساعات المقدرة"
        self.fields['hourly_rate'].label = "معدل الساعة"
        self.fields['sequence'].label = "التسلسل"
        self.fields['assigned_to'].label = "مسند إلى"

class QualityCheckForm(forms.ModelForm):
    class Meta:
        model = QualityCheck
        fields = [
            'check_name', 'description', 'checked_quantity', 
            'passed_quantity', 'failed_quantity', 'status', 'notes'
        ]
        widgets = {
            'check_name': forms.TextInput(attrs={
                'class': 'form-control',
                'required': True,
                'placeholder': 'اسم الفحص'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 2,
                'placeholder': 'وصف الفحص'
            }),
            'checked_quantity': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0',
                'required': True
            }),
            'passed_quantity': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'failed_quantity': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'status': forms.Select(attrs={
                'class': 'form-select'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'ملاحظات الفحص'
            })
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # إضافة تسميات
        self.fields['check_name'].label = "اسم الفحص"
        self.fields['description'].label = "وصف الفحص"
        self.fields['checked_quantity'].label = "الكمية المفحوصة"
        self.fields['passed_quantity'].label = "الكمية الناجحة"
        self.fields['failed_quantity'].label = "الكمية الفاشلة"
        self.fields['status'].label = "النتيجة"
        self.fields['notes'].label = "ملاحظات"

# إنشاء Formsets للمواد والعمليات
ProductionOrderMaterialFormSet = inlineformset_factory(
    ProductionOrder,
    ProductionOrderMaterial,
    form=ProductionOrderMaterialForm,
    extra=1,
    can_delete=True,
    min_num=1,
    validate_min=True
)

ProductionOperationFormSet = inlineformset_factory(
    ProductionOrder,
    ProductionOperation,
    form=ProductionOperationForm,
    extra=1,
    can_delete=True,
    min_num=1,
    validate_min=True
)

class ProductionOrderSearchForm(forms.Form):
    """نموذج البحث في أوامر الإنتاج"""
    search = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'البحث برقم الأمر أو اسم المنتج...'
        })
    )
    
    status = forms.ChoiceField(
        choices=[('', 'جميع الحالات')] + ProductionOrder.STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )
    
    priority = forms.ChoiceField(
        choices=[('', 'جميع الأولويات')] + ProductionOrder.PRIORITY_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )
    
    warehouse = forms.ModelChoiceField(
        queryset=Warehouse.objects.all(),
        required=False,
        empty_label="جميع المخازن",
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )
    
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
    
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
