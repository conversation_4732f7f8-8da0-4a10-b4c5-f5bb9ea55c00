from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, F
from django.db import models
from .models import Product
from .forms import ProductForm

@login_required
def product_list(request):
    search_query = request.GET.get('search', '')
    filter_low_stock = request.GET.get('low_stock', '')

    products = Product.objects.all()

    if search_query:
        products = products.filter(
            Q(name__icontains=search_query) |
            Q(sku__icontains=search_query)
        )

    if filter_low_stock:
        products = products.filter(quantity__lte=F('min_quantity'))

    products = products.order_by('-created_at')

    # Pagination
    paginator = Paginator(products, 12)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Statistics
    total_products = Product.objects.count()
    low_stock_count = Product.objects.filter(quantity__lte=models.F('min_quantity')).count()
    total_value = sum(product.quantity * product.price for product in Product.objects.all())

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'filter_low_stock': filter_low_stock,
        'total_products': total_products,
        'low_stock_count': low_stock_count,
        'total_value': total_value,
    }
    return render(request, 'products/product_list.html', context)

@login_required
def product_create(request):
    if request.method == 'POST':
        form = ProductForm(request.POST)
        if form.is_valid():
            product = form.save()
            messages.success(request, f'تم إضافة المنتج "{product.name}" بنجاح!')
            return redirect('products:product_list')
    else:
        form = ProductForm()

    return render(request, 'products/product_form.html', {
        'form': form,
        'title': 'إضافة منتج جديد',
        'button_text': 'إضافة المنتج'
    })

@login_required
def product_edit(request, pk):
    product = get_object_or_404(Product, pk=pk)

    if request.method == 'POST':
        form = ProductForm(request.POST, instance=product)
        if form.is_valid():
            product = form.save()
            messages.success(request, f'تم تحديث بيانات المنتج "{product.name}" بنجاح!')
            return redirect('products:product_list')
    else:
        form = ProductForm(instance=product)

    return render(request, 'products/product_form.html', {
        'form': form,
        'product': product,
        'title': f'تعديل المنتج: {product.name}',
        'button_text': 'حفظ التغييرات'
    })

@login_required
def product_detail(request, pk):
    product = get_object_or_404(Product, pk=pk)
    return render(request, 'products/product_detail.html', {'product': product})

@login_required
def product_delete(request, pk):
    product = get_object_or_404(Product, pk=pk)

    if request.method == 'POST':
        product_name = product.name
        product.delete()
        messages.success(request, f'تم حذف المنتج "{product_name}" بنجاح!')
        return redirect('products:product_list')

    return render(request, 'products/product_confirm_delete.html', {'product': product})
