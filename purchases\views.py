from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.db.models import Sum
from .models import PurchaseOrder, Supplier

@login_required
def purchases_dashboard(request):
    """لوحة تحكم المشتريات"""
    total_orders = PurchaseOrder.objects.count()
    total_suppliers = Supplier.objects.filter(is_active=True).count()
    total_purchases = PurchaseOrder.objects.filter(status='received').aggregate(
        total=Sum('total_amount'))['total'] or 0
    pending_orders = PurchaseOrder.objects.filter(status__in=['sent', 'confirmed']).count()

    context = {
        'total_orders': total_orders,
        'total_suppliers': total_suppliers,
        'total_purchases': total_purchases,
        'pending_orders': pending_orders,
    }
    return render(request, 'purchases/dashboard.html', context)
