from django.db import models
from django.core.validators import MinValueValidator
from products.models import Product
from branches.models import Branch

class Warehouse(models.Model):
    """المخازن والمستودعات"""
    WAREHOUSE_TYPES = [
        ('main', 'مخزن رئيسي'),
        ('branch', 'مخزن فرع'),
        ('temporary', 'مخزن مؤقت'),
        ('damaged', 'مخزن تالف'),
        ('returns', 'مخزن مرتجعات'),
    ]

    name = models.CharField(max_length=200, verbose_name="اسم المخزن")
    code = models.CharField(max_length=20, unique=True, verbose_name="رمز المخزن")
    warehouse_type = models.CharField(max_length=20, choices=WAREHOUSE_TYPES, verbose_name="نوع المخزن")
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE, verbose_name="الفرع")
    address = models.TextField(verbose_name="العنوان")
    capacity = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name="السعة (متر مكعب)")
    manager_name = models.CharField(max_length=100, blank=True, verbose_name="اسم المسؤول")
    phone = models.CharField(max_length=20, blank=True, verbose_name="رقم الهاتف")
    email = models.EmailField(blank=True, verbose_name="البريد الإلكتروني")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "مخزن"
        verbose_name_plural = "المخازن"
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.code})"

class WarehouseZone(models.Model):
    """مناطق المخزن"""
    warehouse = models.ForeignKey(Warehouse, on_delete=models.CASCADE, related_name='zones', verbose_name="المخزن")
    name = models.CharField(max_length=100, verbose_name="اسم المنطقة")
    code = models.CharField(max_length=20, verbose_name="رمز المنطقة")
    description = models.TextField(blank=True, verbose_name="الوصف")
    temperature_controlled = models.BooleanField(default=False, verbose_name="مكيف الهواء")
    is_active = models.BooleanField(default=True, verbose_name="نشط")

    class Meta:
        verbose_name = "منطقة مخزن"
        verbose_name_plural = "مناطق المخازن"
        unique_together = ['warehouse', 'code']

    def __str__(self):
        return f"{self.warehouse.name} - {self.name}"

class WarehouseLocation(models.Model):
    """مواقع التخزين داخل المخزن"""
    zone = models.ForeignKey(WarehouseZone, on_delete=models.CASCADE, related_name='locations', verbose_name="المنطقة")
    aisle = models.CharField(max_length=10, verbose_name="الممر")
    rack = models.CharField(max_length=10, verbose_name="الرف")
    shelf = models.CharField(max_length=10, verbose_name="الطبقة")
    bin = models.CharField(max_length=10, verbose_name="الصندوق")
    barcode = models.CharField(max_length=50, unique=True, blank=True, verbose_name="الباركود")
    capacity = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True, verbose_name="السعة")
    is_occupied = models.BooleanField(default=False, verbose_name="مشغول")
    is_active = models.BooleanField(default=True, verbose_name="نشط")

    class Meta:
        verbose_name = "موقع تخزين"
        verbose_name_plural = "مواقع التخزين"
        unique_together = ['zone', 'aisle', 'rack', 'shelf', 'bin']

    def __str__(self):
        return f"{self.zone.warehouse.name} - {self.aisle}-{self.rack}-{self.shelf}-{self.bin}"

    @property
    def location_code(self):
        return f"{self.aisle}-{self.rack}-{self.shelf}-{self.bin}"

class Stock(models.Model):
    """المخزون"""
    product = models.ForeignKey(Product, on_delete=models.CASCADE, verbose_name="المنتج")
    warehouse = models.ForeignKey(Warehouse, on_delete=models.CASCADE, verbose_name="المخزن")
    location = models.ForeignKey(WarehouseLocation, on_delete=models.SET_NULL, null=True, blank=True, verbose_name="موقع التخزين")
    quantity = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name="الكمية")
    reserved_quantity = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name="الكمية المحجوزة")
    minimum_stock = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name="الحد الأدنى للمخزون")
    maximum_stock = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name="الحد الأقصى للمخزون")
    reorder_point = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name="نقطة إعادة الطلب")
    last_updated = models.DateTimeField(auto_now=True, verbose_name="آخر تحديث")

    class Meta:
        verbose_name = "مخزون"
        verbose_name_plural = "المخزون"
        unique_together = ['product', 'warehouse']

    def __str__(self):
        return f"{self.product.name} - {self.warehouse.name} ({self.quantity})"

    @property
    def available_quantity(self):
        """الكمية المتاحة = الكمية الكلية - الكمية المحجوزة"""
        return self.quantity - self.reserved_quantity

    @property
    def is_low_stock(self):
        """هل المخزون أقل من الحد الأدنى"""
        return self.quantity <= self.minimum_stock

    @property
    def is_out_of_stock(self):
        """هل المخزون منتهي"""
        return self.quantity <= 0

class StockMovement(models.Model):
    """حركة المخزون"""
    MOVEMENT_TYPES = [
        ('in', 'وارد'),
        ('out', 'صادر'),
        ('transfer', 'تحويل'),
        ('adjustment', 'تسوية'),
        ('damaged', 'تالف'),
        ('returned', 'مرتجع'),
    ]

    REFERENCE_TYPES = [
        ('purchase', 'أمر شراء'),
        ('sale', 'أمر بيع'),
        ('transfer', 'تحويل مخزون'),
        ('adjustment', 'تسوية مخزون'),
        ('production', 'إنتاج'),
        ('return', 'مرتجع'),
        ('damage', 'تلف'),
        ('manual', 'يدوي'),
    ]

    product = models.ForeignKey(Product, on_delete=models.CASCADE, verbose_name="المنتج")
    warehouse = models.ForeignKey(Warehouse, on_delete=models.CASCADE, verbose_name="المخزن")
    location = models.ForeignKey(WarehouseLocation, on_delete=models.SET_NULL, null=True, blank=True, verbose_name="موقع التخزين")
    movement_type = models.CharField(max_length=20, choices=MOVEMENT_TYPES, verbose_name="نوع الحركة")
    reference_type = models.CharField(max_length=20, choices=REFERENCE_TYPES, verbose_name="نوع المرجع")
    reference_number = models.CharField(max_length=100, blank=True, verbose_name="رقم المرجع")
    quantity = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="الكمية")
    unit_cost = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name="تكلفة الوحدة")
    total_cost = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True, verbose_name="التكلفة الإجمالية")
    balance_after = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="الرصيد بعد الحركة")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    created_by = models.CharField(max_length=100, verbose_name="المستخدم")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الحركة")

    class Meta:
        verbose_name = "حركة مخزون"
        verbose_name_plural = "حركات المخزون"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.product.name} - {self.get_movement_type_display()} - {self.quantity}"

class StockTransfer(models.Model):
    """تحويل المخزون بين المخازن"""
    STATUS_CHOICES = [
        ('draft', 'مسودة'),
        ('pending', 'في الانتظار'),
        ('in_transit', 'في الطريق'),
        ('completed', 'مكتمل'),
        ('cancelled', 'ملغي'),
    ]

    transfer_number = models.CharField(max_length=50, unique=True, verbose_name="رقم التحويل")
    from_warehouse = models.ForeignKey(Warehouse, on_delete=models.CASCADE, related_name='transfers_out', verbose_name="من المخزن")
    to_warehouse = models.ForeignKey(Warehouse, on_delete=models.CASCADE, related_name='transfers_in', verbose_name="إلى المخزن")
    transfer_date = models.DateTimeField(verbose_name="تاريخ التحويل")
    expected_arrival = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ الوصول المتوقع")
    actual_arrival = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ الوصول الفعلي")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft', verbose_name="الحالة")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    created_by = models.CharField(max_length=100, verbose_name="المستخدم")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "تحويل مخزون"
        verbose_name_plural = "تحويلات المخزون"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.transfer_number} - {self.from_warehouse.name} → {self.to_warehouse.name}"

class StockTransferItem(models.Model):
    """عناصر تحويل المخزون"""
    transfer = models.ForeignKey(StockTransfer, on_delete=models.CASCADE, related_name='items', verbose_name="التحويل")
    product = models.ForeignKey(Product, on_delete=models.CASCADE, verbose_name="المنتج")
    requested_quantity = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="الكمية المطلوبة")
    sent_quantity = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name="الكمية المرسلة")
    received_quantity = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name="الكمية المستلمة")
    unit_cost = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name="تكلفة الوحدة")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")

    class Meta:
        verbose_name = "عنصر تحويل مخزون"
        verbose_name_plural = "عناصر تحويل المخزون"

    def __str__(self):
        return f"{self.product.name} - {self.requested_quantity}"

class StockAdjustment(models.Model):
    """تسوية المخزون"""
    ADJUSTMENT_TYPES = [
        ('increase', 'زيادة'),
        ('decrease', 'نقص'),
        ('correction', 'تصحيح'),
    ]

    adjustment_number = models.CharField(max_length=50, unique=True, verbose_name="رقم التسوية")
    warehouse = models.ForeignKey(Warehouse, on_delete=models.CASCADE, verbose_name="المخزن")
    adjustment_type = models.CharField(max_length=20, choices=ADJUSTMENT_TYPES, verbose_name="نوع التسوية")
    adjustment_date = models.DateTimeField(verbose_name="تاريخ التسوية")
    reason = models.TextField(verbose_name="سبب التسوية")
    approved_by = models.CharField(max_length=100, blank=True, verbose_name="معتمد من")
    created_by = models.CharField(max_length=100, verbose_name="المستخدم")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "تسوية مخزون"
        verbose_name_plural = "تسويات المخزون"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.adjustment_number} - {self.warehouse.name}"

class StockAdjustmentItem(models.Model):
    """عناصر تسوية المخزون"""
    adjustment = models.ForeignKey(StockAdjustment, on_delete=models.CASCADE, related_name='items', verbose_name="التسوية")
    product = models.ForeignKey(Product, on_delete=models.CASCADE, verbose_name="المنتج")
    current_quantity = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="الكمية الحالية")
    adjusted_quantity = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="الكمية بعد التسوية")
    difference = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="الفرق")
    unit_cost = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name="تكلفة الوحدة")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")

    class Meta:
        verbose_name = "عنصر تسوية مخزون"
        verbose_name_plural = "عناصر تسوية المخزون"

    def __str__(self):
        return f"{self.product.name} - {self.difference}"

    def save(self, *args, **kwargs):
        self.difference = self.adjusted_quantity - self.current_quantity
        super().save(*args, **kwargs)
