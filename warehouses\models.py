from django.db import models
from django.contrib.auth.models import User
from definitions.models import WarehouseDefinition, ProductDefinition
from decimal import Decimal

class InventoryTransaction(models.Model):
    """حركات المخزون"""
    TRANSACTION_TYPES = [
        ('in', 'إدخال'),
        ('out', 'إخراج'),
        ('transfer', 'نقل'),
        ('adjustment', 'تسوية'),
    ]

    TRANSACTION_REASONS = [
        ('purchase', 'شراء'),
        ('sale', 'بيع'),
        ('return_in', 'مرتجع وارد'),
        ('return_out', 'مرتجع صادر'),
        ('damage', 'تالف'),
        ('expired', 'منتهي الصلاحية'),
        ('transfer', 'نقل بين مخازن'),
        ('adjustment', 'تسوية جرد'),
        ('production', 'إنتاج'),
        ('initial', 'رصيد افتتاحي'),
    ]

    transaction_number = models.CharField(max_length=50, unique=True, verbose_name="رقم الحركة")
    warehouse = models.ForeignKey(WarehouseDefinition, on_delete=models.CASCADE, verbose_name="المخزن")
    product = models.ForeignKey(ProductDefinition, on_delete=models.CASCADE, verbose_name="الصنف")
    transaction_type = models.CharField(max_length=20, choices=TRANSACTION_TYPES, verbose_name="نوع الحركة")
    transaction_reason = models.CharField(max_length=20, choices=TRANSACTION_REASONS, verbose_name="سبب الحركة")
    quantity = models.DecimalField(max_digits=15, decimal_places=3, verbose_name="الكمية")
    unit_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="تكلفة الوحدة")
    total_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="إجمالي التكلفة")
    reference_number = models.CharField(max_length=100, blank=True, null=True, verbose_name="رقم المرجع")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    transaction_date = models.DateTimeField(verbose_name="تاريخ الحركة")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="المستخدم")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    # للنقل بين المخازن
    to_warehouse = models.ForeignKey(
        WarehouseDefinition,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='transfers_in',
        verbose_name="المخزن المستقبل"
    )

    class Meta:
        verbose_name = "حركة مخزون"
        verbose_name_plural = "حركات المخزون"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.transaction_number} - {self.product.name}"

    def save(self, *args, **kwargs):
        if not self.transaction_number:
            self.transaction_number = self.generate_transaction_number()

        # حساب إجمالي التكلفة
        self.total_cost = self.quantity * self.unit_cost

        super().save(*args, **kwargs)

        # تحديث رصيد المخزون
        self.update_inventory_balance()

    def generate_transaction_number(self):
        """توليد رقم حركة تلقائي"""
        from django.utils import timezone
        today = timezone.now().date()
        prefix = f"TXN{today.strftime('%Y%m%d')}"

        last_transaction = InventoryTransaction.objects.filter(
            transaction_number__startswith=prefix
        ).order_by('-transaction_number').first()

        if last_transaction:
            last_number = int(last_transaction.transaction_number[-4:])
            new_number = last_number + 1
        else:
            new_number = 1

        return f"{prefix}{new_number:04d}"

    def update_inventory_balance(self):
        """تحديث رصيد المخزون"""
        balance, created = InventoryBalance.objects.get_or_create(
            warehouse=self.warehouse,
            product=self.product,
            defaults={'quantity': 0, 'total_cost': 0}
        )

        if self.transaction_type == 'in':
            balance.quantity += self.quantity
            balance.total_cost += self.total_cost
        elif self.transaction_type == 'out':
            balance.quantity -= self.quantity
            balance.total_cost -= self.total_cost
        elif self.transaction_type == 'transfer':
            # خصم من المخزن المرسل
            balance.quantity -= self.quantity
            balance.total_cost -= self.total_cost

            # إضافة للمخزن المستقبل
            if self.to_warehouse:
                to_balance, created = InventoryBalance.objects.get_or_create(
                    warehouse=self.to_warehouse,
                    product=self.product,
                    defaults={'quantity': 0, 'total_cost': 0}
                )
                to_balance.quantity += self.quantity
                to_balance.total_cost += self.total_cost
                to_balance.save()

        balance.save()


class InventoryBalance(models.Model):
    """أرصدة المخزون"""
    warehouse = models.ForeignKey(WarehouseDefinition, on_delete=models.CASCADE, verbose_name="المخزن")
    product = models.ForeignKey(ProductDefinition, on_delete=models.CASCADE, verbose_name="الصنف")
    quantity = models.DecimalField(max_digits=15, decimal_places=3, default=0, verbose_name="الكمية")
    total_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="إجمالي التكلفة")
    last_updated = models.DateTimeField(auto_now=True, verbose_name="آخر تحديث")

    class Meta:
        verbose_name = "رصيد مخزون"
        verbose_name_plural = "أرصدة المخزون"
        unique_together = ['warehouse', 'product']

    def __str__(self):
        return f"{self.warehouse.name} - {self.product.name}: {self.quantity}"

    @property
    def average_cost(self):
        """متوسط التكلفة"""
        if self.quantity > 0:
            return self.total_cost / self.quantity
        return Decimal('0.00')


class StockAdjustment(models.Model):
    """تسويات المخزون"""
    ADJUSTMENT_TYPES = [
        ('increase', 'زيادة'),
        ('decrease', 'نقص'),
        ('correction', 'تصحيح'),
    ]

    adjustment_number = models.CharField(max_length=50, unique=True, verbose_name="رقم التسوية")
    warehouse = models.ForeignKey(WarehouseDefinition, on_delete=models.CASCADE, verbose_name="المخزن")
    adjustment_type = models.CharField(max_length=20, choices=ADJUSTMENT_TYPES, verbose_name="نوع التسوية")
    adjustment_date = models.DateTimeField(verbose_name="تاريخ التسوية")
    reason = models.TextField(verbose_name="سبب التسوية")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="المستخدم")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    approved = models.BooleanField(default=False, verbose_name="معتمد")
    approved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_adjustments',
        verbose_name="معتمد بواسطة"
    )
    approved_at = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ الاعتماد")

    class Meta:
        verbose_name = "تسوية مخزون"
        verbose_name_plural = "تسويات المخزون"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.adjustment_number} - {self.warehouse.name}"


class StockAdjustmentItem(models.Model):
    """عناصر تسوية المخزون"""
    adjustment = models.ForeignKey(StockAdjustment, on_delete=models.CASCADE, related_name='items', verbose_name="التسوية")
    product = models.ForeignKey(ProductDefinition, on_delete=models.CASCADE, verbose_name="الصنف")
    current_quantity = models.DecimalField(max_digits=15, decimal_places=3, verbose_name="الكمية الحالية")
    actual_quantity = models.DecimalField(max_digits=15, decimal_places=3, verbose_name="الكمية الفعلية")
    difference = models.DecimalField(max_digits=15, decimal_places=3, verbose_name="الفرق")
    unit_cost = models.DecimalField(max_digits=15, decimal_places=2, verbose_name="تكلفة الوحدة")
    total_cost_difference = models.DecimalField(max_digits=15, decimal_places=2, verbose_name="فرق التكلفة")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")

    class Meta:
        verbose_name = "عنصر تسوية"
        verbose_name_plural = "عناصر التسوية"

    def save(self, *args, **kwargs):
        self.difference = self.actual_quantity - self.current_quantity
        self.total_cost_difference = self.difference * self.unit_cost
        super().save(*args, **kwargs)
