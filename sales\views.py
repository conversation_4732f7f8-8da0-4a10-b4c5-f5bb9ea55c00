from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.db.models import Sum
from .models import SalesOrder, SalesInvoice

@login_required
def sales_dashboard(request):
    """لوحة تحكم المبيعات"""
    total_orders = SalesOrder.objects.count()
    total_invoices = SalesInvoice.objects.count()
    total_sales = SalesInvoice.objects.filter(status='paid').aggregate(
        total=Sum('total_amount'))['total'] or 0
    pending_invoices = SalesInvoice.objects.filter(status='sent').count()

    context = {
        'total_orders': total_orders,
        'total_invoices': total_invoices,
        'total_sales': total_sales,
        'pending_invoices': pending_invoices,
    }
    return render(request, 'sales/dashboard.html', context)
