# Generated by Django 5.2.4 on 2025-07-11 10:52

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('branches', '0001_initial'),
        ('products', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='StockAdjustment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('adjustment_number', models.CharField(max_length=50, unique=True, verbose_name='رقم التسوية')),
                ('adjustment_type', models.CharField(choices=[('increase', 'زيادة'), ('decrease', 'نقص'), ('correction', 'تصحيح')], max_length=20, verbose_name='نوع التسوية')),
                ('adjustment_date', models.DateTimeField(verbose_name='تاريخ التسوية')),
                ('reason', models.TextField(verbose_name='سبب التسوية')),
                ('approved_by', models.CharField(blank=True, max_length=100, verbose_name='معتمد من')),
                ('created_by', models.CharField(max_length=100, verbose_name='المستخدم')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'تسوية مخزون',
                'verbose_name_plural': 'تسويات المخزون',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='StockTransfer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transfer_number', models.CharField(max_length=50, unique=True, verbose_name='رقم التحويل')),
                ('transfer_date', models.DateTimeField(verbose_name='تاريخ التحويل')),
                ('expected_arrival', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الوصول المتوقع')),
                ('actual_arrival', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الوصول الفعلي')),
                ('status', models.CharField(choices=[('draft', 'مسودة'), ('pending', 'في الانتظار'), ('in_transit', 'في الطريق'), ('completed', 'مكتمل'), ('cancelled', 'ملغي')], default='draft', max_length=20, verbose_name='الحالة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.CharField(max_length=100, verbose_name='المستخدم')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'تحويل مخزون',
                'verbose_name_plural': 'تحويلات المخزون',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='WarehouseLocation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('aisle', models.CharField(max_length=10, verbose_name='الممر')),
                ('rack', models.CharField(max_length=10, verbose_name='الرف')),
                ('shelf', models.CharField(max_length=10, verbose_name='الطبقة')),
                ('bin', models.CharField(max_length=10, verbose_name='الصندوق')),
                ('barcode', models.CharField(blank=True, max_length=50, unique=True, verbose_name='الباركود')),
                ('capacity', models.DecimalField(blank=True, decimal_places=2, max_digits=8, null=True, verbose_name='السعة')),
                ('is_occupied', models.BooleanField(default=False, verbose_name='مشغول')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
            ],
            options={
                'verbose_name': 'موقع تخزين',
                'verbose_name_plural': 'مواقع التخزين',
            },
        ),
        migrations.CreateModel(
            name='StockAdjustmentItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('current_quantity', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الكمية الحالية')),
                ('adjusted_quantity', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الكمية بعد التسوية')),
                ('difference', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الفرق')),
                ('unit_cost', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='تكلفة الوحدة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('adjustment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='warehouses.stockadjustment', verbose_name='التسوية')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='products.product', verbose_name='المنتج')),
            ],
            options={
                'verbose_name': 'عنصر تسوية مخزون',
                'verbose_name_plural': 'عناصر تسوية المخزون',
            },
        ),
        migrations.CreateModel(
            name='StockTransferItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('requested_quantity', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الكمية المطلوبة')),
                ('sent_quantity', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الكمية المرسلة')),
                ('received_quantity', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الكمية المستلمة')),
                ('unit_cost', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='تكلفة الوحدة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='products.product', verbose_name='المنتج')),
                ('transfer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='warehouses.stocktransfer', verbose_name='التحويل')),
            ],
            options={
                'verbose_name': 'عنصر تحويل مخزون',
                'verbose_name_plural': 'عناصر تحويل المخزون',
            },
        ),
        migrations.CreateModel(
            name='Warehouse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم المخزن')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='رمز المخزن')),
                ('warehouse_type', models.CharField(choices=[('main', 'مخزن رئيسي'), ('branch', 'مخزن فرع'), ('temporary', 'مخزن مؤقت'), ('damaged', 'مخزن تالف'), ('returns', 'مخزن مرتجعات')], max_length=20, verbose_name='نوع المخزن')),
                ('address', models.TextField(verbose_name='العنوان')),
                ('capacity', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='السعة (متر مكعب)')),
                ('manager_name', models.CharField(blank=True, max_length=100, verbose_name='اسم المسؤول')),
                ('phone', models.CharField(blank=True, max_length=20, verbose_name='رقم الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='البريد الإلكتروني')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='branches.branch', verbose_name='الفرع')),
            ],
            options={
                'verbose_name': 'مخزن',
                'verbose_name_plural': 'المخازن',
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='stocktransfer',
            name='from_warehouse',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transfers_out', to='warehouses.warehouse', verbose_name='من المخزن'),
        ),
        migrations.AddField(
            model_name='stocktransfer',
            name='to_warehouse',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transfers_in', to='warehouses.warehouse', verbose_name='إلى المخزن'),
        ),
        migrations.AddField(
            model_name='stockadjustment',
            name='warehouse',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='warehouses.warehouse', verbose_name='المخزن'),
        ),
        migrations.CreateModel(
            name='StockMovement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('movement_type', models.CharField(choices=[('in', 'وارد'), ('out', 'صادر'), ('transfer', 'تحويل'), ('adjustment', 'تسوية'), ('damaged', 'تالف'), ('returned', 'مرتجع')], max_length=20, verbose_name='نوع الحركة')),
                ('reference_type', models.CharField(choices=[('purchase', 'أمر شراء'), ('sale', 'أمر بيع'), ('transfer', 'تحويل مخزون'), ('adjustment', 'تسوية مخزون'), ('production', 'إنتاج'), ('return', 'مرتجع'), ('damage', 'تلف'), ('manual', 'يدوي')], max_length=20, verbose_name='نوع المرجع')),
                ('reference_number', models.CharField(blank=True, max_length=100, verbose_name='رقم المرجع')),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الكمية')),
                ('unit_cost', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='تكلفة الوحدة')),
                ('total_cost', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='التكلفة الإجمالية')),
                ('balance_after', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الرصيد بعد الحركة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.CharField(max_length=100, verbose_name='المستخدم')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الحركة')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='products.product', verbose_name='المنتج')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='warehouses.warehouse', verbose_name='المخزن')),
                ('location', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='warehouses.warehouselocation', verbose_name='موقع التخزين')),
            ],
            options={
                'verbose_name': 'حركة مخزون',
                'verbose_name_plural': 'حركات المخزون',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='WarehouseZone',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم المنطقة')),
                ('code', models.CharField(max_length=20, verbose_name='رمز المنطقة')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('temperature_controlled', models.BooleanField(default=False, verbose_name='مكيف الهواء')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='zones', to='warehouses.warehouse', verbose_name='المخزن')),
            ],
            options={
                'verbose_name': 'منطقة مخزن',
                'verbose_name_plural': 'مناطق المخازن',
                'unique_together': {('warehouse', 'code')},
            },
        ),
        migrations.AddField(
            model_name='warehouselocation',
            name='zone',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='locations', to='warehouses.warehousezone', verbose_name='المنطقة'),
        ),
        migrations.CreateModel(
            name='Stock',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الكمية')),
                ('reserved_quantity', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الكمية المحجوزة')),
                ('minimum_stock', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الحد الأدنى للمخزون')),
                ('maximum_stock', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='الحد الأقصى للمخزون')),
                ('reorder_point', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='نقطة إعادة الطلب')),
                ('last_updated', models.DateTimeField(auto_now=True, verbose_name='آخر تحديث')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='products.product', verbose_name='المنتج')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='warehouses.warehouse', verbose_name='المخزن')),
                ('location', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='warehouses.warehouselocation', verbose_name='موقع التخزين')),
            ],
            options={
                'verbose_name': 'مخزون',
                'verbose_name_plural': 'المخزون',
                'unique_together': {('product', 'warehouse')},
            },
        ),
        migrations.AlterUniqueTogether(
            name='warehouselocation',
            unique_together={('zone', 'aisle', 'rack', 'shelf', 'bin')},
        ),
    ]
