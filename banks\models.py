from django.db import models
from django.core.validators import RegexValidator
from definitions.models import Currency

class Bank(models.Model):
    """نموذج البنوك"""
    name = models.CharField(max_length=200, verbose_name="اسم البنك")
    code = models.CharField(max_length=20, unique=True, verbose_name="رمز البنك")
    swift_code = models.CharField(max_length=11, blank=True, verbose_name="رمز SWIFT")
    address = models.TextField(blank=True, verbose_name="العنوان")
    phone = models.CharField(max_length=20, blank=True, verbose_name="رقم الهاتف")
    email = models.EmailField(blank=True, verbose_name="البريد الإلكتروني")
    website = models.URLField(blank=True, verbose_name="الموقع الإلكتروني")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "بنك"
        verbose_name_plural = "البنوك"
        ordering = ['name']

    def __str__(self):
        return self.name

class BankAccount(models.Model):
    """نموذج الحسابات البنكية"""
    ACCOUNT_TYPES = [
        ('current', 'حساب جاري'),
        ('savings', 'حساب توفير'),
        ('fixed', 'وديعة ثابتة'),
        ('investment', 'حساب استثماري'),
    ]

    bank = models.ForeignKey(Bank, on_delete=models.CASCADE, verbose_name="البنك")
    account_name = models.CharField(max_length=200, verbose_name="اسم الحساب")
    account_number = models.CharField(max_length=50, verbose_name="رقم الحساب")
    iban = models.CharField(max_length=34, blank=True, verbose_name="رقم IBAN")
    account_type = models.CharField(max_length=20, choices=ACCOUNT_TYPES, verbose_name="نوع الحساب")
    currency = models.ForeignKey(Currency, on_delete=models.CASCADE, verbose_name="العملة")
    balance = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="الرصيد")
    opening_balance = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="الرصيد الافتتاحي")
    opening_date = models.DateField(verbose_name="تاريخ فتح الحساب")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "حساب بنكي"
        verbose_name_plural = "الحسابات البنكية"
        ordering = ['bank', 'account_name']
        unique_together = ['bank', 'account_number']

    def __str__(self):
        return f"{self.account_name} - {self.bank.name}"

class BankTransaction(models.Model):
    """نموذج المعاملات البنكية"""
    TRANSACTION_TYPES = [
        ('deposit', 'إيداع'),
        ('withdrawal', 'سحب'),
        ('transfer_in', 'تحويل وارد'),
        ('transfer_out', 'تحويل صادر'),
        ('fee', 'رسوم'),
        ('interest', 'فوائد'),
    ]

    account = models.ForeignKey(BankAccount, on_delete=models.CASCADE, verbose_name="الحساب البنكي")
    transaction_type = models.CharField(max_length=20, choices=TRANSACTION_TYPES, verbose_name="نوع المعاملة")
    amount = models.DecimalField(max_digits=15, decimal_places=2, verbose_name="المبلغ")
    description = models.CharField(max_length=500, verbose_name="الوصف")
    reference_number = models.CharField(max_length=100, blank=True, verbose_name="رقم المرجع")
    transaction_date = models.DateTimeField(verbose_name="تاريخ المعاملة")
    balance_after = models.DecimalField(max_digits=15, decimal_places=2, verbose_name="الرصيد بعد المعاملة")
    is_reconciled = models.BooleanField(default=False, verbose_name="تم التسوية")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "معاملة بنكية"
        verbose_name_plural = "المعاملات البنكية"
        ordering = ['-transaction_date']

    def __str__(self):
        return f"{self.get_transaction_type_display()} - {self.amount} - {self.account.account_name}"
