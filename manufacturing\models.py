from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator
from decimal import Decimal
from products.models import Product
from warehouses.models import Warehouse, Stock

class ProductionOrder(models.Model):
    """أمر إنتاج"""
    STATUS_CHOICES = [
        ('draft', 'مسودة'),
        ('confirmed', 'مؤكد'),
        ('in_progress', 'قيد التنفيذ'),
        ('completed', 'مكتمل'),
        ('cancelled', 'ملغي'),
    ]

    PRIORITY_CHOICES = [
        ('low', 'منخفضة'),
        ('normal', 'عادية'),
        ('high', 'عالية'),
        ('urgent', 'عاجلة'),
    ]

    order_number = models.CharField(max_length=50, unique=True, verbose_name="رقم أمر الإنتاج")
    product = models.ForeignKey(Product, on_delete=models.CASCADE, verbose_name="المنتج المراد تصنيعه")
    quantity = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0.01)], verbose_name="الكمية المطلوبة")
    target_warehouse = models.ForeignKey(Warehouse, on_delete=models.CASCADE, verbose_name="مخزن الإنتاج النهائي")

    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft', verbose_name="الحالة")
    priority = models.CharField(max_length=20, choices=PRIORITY_CHOICES, default='normal', verbose_name="الأولوية")

    planned_start_date = models.DateTimeField(verbose_name="تاريخ البدء المخطط")
    planned_end_date = models.DateTimeField(verbose_name="تاريخ الانتهاء المخطط")
    actual_start_date = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ البدء الفعلي")
    actual_end_date = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ الانتهاء الفعلي")

    total_material_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="إجمالي تكلفة المواد")
    total_labor_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="إجمالي تكلفة العمالة")
    total_overhead_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="إجمالي التكاليف الإضافية")
    total_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="إجمالي التكلفة")
    unit_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="تكلفة الوحدة")

    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشئ بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "أمر إنتاج"
        verbose_name_plural = "أوامر الإنتاج"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.order_number} - {self.product.name}"

    def calculate_costs(self):
        """حساب التكاليف تلقائياً"""
        # حساب تكلفة المواد
        material_cost = sum(item.total_cost for item in self.materials.all())

        # حساب تكلفة العمالة
        labor_cost = sum(operation.total_cost for operation in self.operations.all())

        # حساب التكاليف الإضافية (نسبة من تكلفة المواد والعمالة)
        overhead_rate = Decimal('0.15')  # 15% كنسبة افتراضية
        overhead_cost = (material_cost + labor_cost) * overhead_rate

        # إجمالي التكلفة
        total = material_cost + labor_cost + overhead_cost

        # تكلفة الوحدة
        unit_cost = total / self.quantity if self.quantity > 0 else Decimal('0')

        # تحديث الحقول
        self.total_material_cost = material_cost
        self.total_labor_cost = labor_cost
        self.total_overhead_cost = overhead_cost
        self.total_cost = total
        self.unit_cost = unit_cost

        return {
            'material_cost': material_cost,
            'labor_cost': labor_cost,
            'overhead_cost': overhead_cost,
            'total_cost': total,
            'unit_cost': unit_cost
        }

class ProductionOrderMaterial(models.Model):
    """مواد أمر الإنتاج"""
    production_order = models.ForeignKey(ProductionOrder, on_delete=models.CASCADE, related_name='materials', verbose_name="أمر الإنتاج")
    material = models.ForeignKey(Product, on_delete=models.CASCADE, verbose_name="المادة الخام")
    warehouse = models.ForeignKey(Warehouse, on_delete=models.CASCADE, verbose_name="مخزن المادة الخام")

    required_quantity = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0.01)], verbose_name="الكمية المطلوبة")
    available_quantity = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name="الكمية المتاحة")
    unit_cost = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name="تكلفة الوحدة")
    total_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="إجمالي التكلفة")

    allocated = models.BooleanField(default=False, verbose_name="تم التخصيص")
    consumed = models.BooleanField(default=False, verbose_name="تم الاستهلاك")

    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإضافة")

    class Meta:
        verbose_name = "مادة أمر الإنتاج"
        verbose_name_plural = "مواد أوامر الإنتاج"
        unique_together = ['production_order', 'material', 'warehouse']

    def __str__(self):
        return f"{self.material.name} - {self.production_order.order_number}"

    def save(self, *args, **kwargs):
        # حساب التكلفة الإجمالية
        self.total_cost = self.required_quantity * self.unit_cost

        # الحصول على الكمية المتاحة من المخزون
        try:
            stock = Stock.objects.get(product=self.material, warehouse=self.warehouse)
            self.available_quantity = stock.quantity
        except Stock.DoesNotExist:
            self.available_quantity = 0

        super().save(*args, **kwargs)

class ProductionOperation(models.Model):
    """عمليات الإنتاج"""
    production_order = models.ForeignKey(ProductionOrder, on_delete=models.CASCADE, related_name='operations', verbose_name="أمر الإنتاج")
    operation_name = models.CharField(max_length=100, verbose_name="اسم العملية")
    description = models.TextField(blank=True, verbose_name="وصف العملية")

    estimated_hours = models.DecimalField(max_digits=8, decimal_places=2, validators=[MinValueValidator(0)], verbose_name="الساعات المقدرة")
    actual_hours = models.DecimalField(max_digits=8, decimal_places=2, default=0, verbose_name="الساعات الفعلية")
    hourly_rate = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name="معدل الساعة")
    total_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="إجمالي التكلفة")

    sequence = models.PositiveIntegerField(default=1, verbose_name="التسلسل")
    status = models.CharField(max_length=20, choices=[
        ('pending', 'في الانتظار'),
        ('in_progress', 'قيد التنفيذ'),
        ('completed', 'مكتمل'),
        ('cancelled', 'ملغي'),
    ], default='pending', verbose_name="الحالة")

    start_time = models.DateTimeField(null=True, blank=True, verbose_name="وقت البدء")
    end_time = models.DateTimeField(null=True, blank=True, verbose_name="وقت الانتهاء")

    assigned_to = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, verbose_name="مسند إلى")

    class Meta:
        verbose_name = "عملية إنتاج"
        verbose_name_plural = "عمليات الإنتاج"
        ordering = ['sequence']

    def __str__(self):
        return f"{self.operation_name} - {self.production_order.order_number}"

    def save(self, *args, **kwargs):
        # حساب التكلفة الإجمالية
        hours = self.actual_hours if self.actual_hours > 0 else self.estimated_hours
        self.total_cost = hours * self.hourly_rate
        super().save(*args, **kwargs)

class QualityCheck(models.Model):
    """فحص الجودة"""
    production_order = models.ForeignKey(ProductionOrder, on_delete=models.CASCADE, related_name='quality_checks', verbose_name="أمر الإنتاج")
    check_name = models.CharField(max_length=100, verbose_name="اسم الفحص")
    description = models.TextField(blank=True, verbose_name="وصف الفحص")

    STATUS_CHOICES = [
        ('pending', 'في الانتظار'),
        ('passed', 'نجح'),
        ('failed', 'فشل'),
        ('rework', 'إعادة عمل'),
    ]

    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name="النتيجة")
    checked_quantity = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name="الكمية المفحوصة")
    passed_quantity = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name="الكمية الناجحة")
    failed_quantity = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name="الكمية الفاشلة")

    checked_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="فحص بواسطة")
    check_date = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الفحص")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")

    class Meta:
        verbose_name = "فحص جودة"
        verbose_name_plural = "فحوصات الجودة"
        ordering = ['-check_date']

    def __str__(self):
        return f"{self.check_name} - {self.production_order.order_number}"

class ProductionReport(models.Model):
    """تقرير الإنتاج"""
    production_order = models.OneToOneField(ProductionOrder, on_delete=models.CASCADE, related_name='report', verbose_name="أمر الإنتاج")

    produced_quantity = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name="الكمية المنتجة")
    waste_quantity = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name="كمية الفاقد")
    efficiency_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0, verbose_name="نسبة الكفاءة")

    actual_material_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="التكلفة الفعلية للمواد")
    actual_labor_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="التكلفة الفعلية للعمالة")
    actual_overhead_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="التكلفة الفعلية الإضافية")
    actual_total_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="إجمالي التكلفة الفعلية")

    variance_material = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="انحراف تكلفة المواد")
    variance_labor = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="انحراف تكلفة العمالة")
    variance_total = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="إجمالي الانحراف")

    report_date = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ التقرير")
    generated_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشئ بواسطة")

    class Meta:
        verbose_name = "تقرير إنتاج"
        verbose_name_plural = "تقارير الإنتاج"

    def __str__(self):
        return f"تقرير {self.production_order.order_number}"
