from django.db import models
from customers.models import Customer
from products.models import Product
from definitions.models import Currency, TaxRate

class SalesOrder(models.Model):
    """أوامر البيع"""
    STATUS_CHOICES = [
        ('draft', 'مسودة'),
        ('confirmed', 'مؤكد'),
        ('shipped', 'تم الشحن'),
        ('delivered', 'تم التسليم'),
        ('cancelled', 'ملغي'),
    ]

    order_number = models.CharField(max_length=50, unique=True, verbose_name="رقم الطلب")
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name='sales_orders', verbose_name="العميل")
    order_date = models.DateField(verbose_name="تاريخ الطلب")
    delivery_date = models.DateField(null=True, blank=True, verbose_name="تاريخ التسليم المتوقع")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft', verbose_name="الحالة")
    currency = models.ForeignKey(Currency, on_delete=models.CASCADE, verbose_name="العملة")
    subtotal = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="المجموع الفرعي")
    tax_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="مبلغ الضريبة")
    total_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="المجموع الكلي")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "أمر بيع"
        verbose_name_plural = "أوامر البيع"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.order_number} - {self.customer.name}"

class SalesInvoice(models.Model):
    """فواتير المبيعات"""
    STATUS_CHOICES = [
        ('draft', 'مسودة'),
        ('sent', 'مرسلة'),
        ('paid', 'مدفوعة'),
        ('overdue', 'متأخرة'),
        ('cancelled', 'ملغية'),
    ]

    invoice_number = models.CharField(max_length=50, unique=True, verbose_name="رقم الفاتورة")
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name='sales_invoices', verbose_name="العميل")
    sales_order = models.ForeignKey(SalesOrder, on_delete=models.SET_NULL, null=True, blank=True, verbose_name="أمر البيع")
    invoice_date = models.DateField(verbose_name="تاريخ الفاتورة")
    due_date = models.DateField(verbose_name="تاريخ الاستحقاق")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft', verbose_name="الحالة")
    currency = models.ForeignKey(Currency, on_delete=models.CASCADE, verbose_name="العملة")
    subtotal = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="المجموع الفرعي")
    tax_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="مبلغ الضريبة")
    total_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="المجموع الكلي")
    paid_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="المبلغ المدفوع")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "فاتورة مبيعات"
        verbose_name_plural = "فواتير المبيعات"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.invoice_number} - {self.customer.name}"

    @property
    def remaining_amount(self):
        return self.total_amount - self.paid_amount
