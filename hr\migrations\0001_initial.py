# Generated by Django 5.2.4 on 2025-07-11 04:51

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('branches', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Department',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم القسم')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='رمز القسم')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'قسم',
                'verbose_name_plural': 'الأقسام',
            },
        ),
        migrations.CreateModel(
            name='Employee',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('employee_id', models.CharField(max_length=20, unique=True, verbose_name='رقم الموظف')),
                ('first_name', models.CharField(max_length=50, verbose_name='الاسم الأول')),
                ('last_name', models.CharField(max_length=50, verbose_name='اسم العائلة')),
                ('national_id', models.CharField(max_length=20, unique=True, verbose_name='رقم الهوية')),
                ('birth_date', models.DateField(verbose_name='تاريخ الميلاد')),
                ('gender', models.CharField(choices=[('male', 'ذكر'), ('female', 'أنثى')], max_length=10, verbose_name='الجنس')),
                ('marital_status', models.CharField(choices=[('single', 'أعزب'), ('married', 'متزوج'), ('divorced', 'مطلق'), ('widowed', 'أرمل')], max_length=10, verbose_name='الحالة الاجتماعية')),
                ('phone', models.CharField(max_length=20, verbose_name='رقم الهاتف')),
                ('email', models.EmailField(max_length=254, verbose_name='البريد الإلكتروني')),
                ('address', models.TextField(verbose_name='العنوان')),
                ('hire_date', models.DateField(verbose_name='تاريخ التوظيف')),
                ('salary', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الراتب')),
                ('status', models.CharField(choices=[('active', 'نشط'), ('inactive', 'غير نشط'), ('terminated', 'منتهي الخدمة'), ('suspended', 'موقوف')], default='active', max_length=20, verbose_name='الحالة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='branches.branch', verbose_name='الفرع')),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='hr.department', verbose_name='القسم')),
                ('user', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='حساب المستخدم')),
            ],
            options={
                'verbose_name': 'موظف',
                'verbose_name_plural': 'الموظفين',
                'ordering': ['employee_id'],
            },
        ),
        migrations.AddField(
            model_name='department',
            name='manager',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_departments', to='hr.employee', verbose_name='مدير القسم'),
        ),
        migrations.CreateModel(
            name='Position',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم المنصب')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='رمز المنصب')),
                ('basic_salary', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الراتب الأساسي')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='hr.department', verbose_name='القسم')),
            ],
            options={
                'verbose_name': 'منصب وظيفي',
                'verbose_name_plural': 'المناصب الوظيفية',
            },
        ),
        migrations.AddField(
            model_name='employee',
            name='position',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='hr.position', verbose_name='المنصب'),
        ),
    ]
