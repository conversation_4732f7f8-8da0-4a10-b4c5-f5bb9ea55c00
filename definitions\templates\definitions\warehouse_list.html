{% extends 'base.html' %}
{% load static %}

{% block title %}تعريف المخازن{% endblock %}

{% block extra_css %}
<style>
    .warehouse-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 2rem 0;
        margin-bottom: 2rem;
        color: white;
        border-radius: 15px;
    }

    .search-filters {
        background: white;
        padding: 1.5rem;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
    }

    .warehouse-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        overflow: hidden;
        margin-bottom: 1.5rem;
    }

    .warehouse-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .warehouse-card-header {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        padding: 1rem 1.5rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .warehouse-type-badge {
        background: rgba(255,255,255,0.2);
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
    }

    .warehouse-card-body {
        padding: 1.5rem;
    }

    .warehouse-info {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .info-item {
        display: flex;
        align-items: center;
        color: #666;
    }

    .info-item i {
        margin-left: 0.5rem;
        color: #667eea;
        width: 20px;
    }

    .warehouse-actions {
        display: flex;
        gap: 0.5rem;
        justify-content: flex-end;
    }

    .btn-action {
        padding: 0.5rem 1rem;
        border-radius: 8px;
        border: none;
        font-size: 0.85rem;
        font-weight: 500;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
    }

    .btn-view {
        background: #e3f2fd;
        color: #1976d2;
    }

    .btn-edit {
        background: #fff3e0;
        color: #f57c00;
    }

    .btn-delete {
        background: #ffebee;
        color: #d32f2f;
    }

    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .pagination-wrapper {
        display: flex;
        justify-content: center;
        margin-top: 2rem;
    }

    .stats-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: white;
        padding: 1.5rem;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        text-align: center;
    }

    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: #667eea;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        color: #666;
        font-size: 0.9rem;
    }
</style>
{% endblock %}

{% block content %}
<!-- Warehouse Header -->
<div class="warehouse-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-building me-2"></i>
                    تعريف المخازن
                </h1>
                <p class="mb-0 opacity-75">عرض وإدارة جميع المخازن والمستودعات</p>
            </div>
            <div class="col-md-4 text-end">
                <a href="#" class="btn btn-light btn-lg">
                    <i class="bi bi-plus-circle me-2"></i>
                    إضافة مخزن جديد
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Statistics -->
<div class="container-fluid">
    <div class="stats-row">
        <div class="stat-card">
            <div class="stat-number">{{ total_warehouses }}</div>
            <div class="stat-label">إجمالي المخازن</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ page_obj.paginator.count }}</div>
            <div class="stat-label">المخازن المعروضة</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ page_obj.paginator.num_pages }}</div>
            <div class="stat-label">عدد الصفحات</div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="search-filters">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label class="form-label">البحث</label>
                <input type="text" class="form-control" name="search" value="{{ search_query }}" 
                       placeholder="البحث بالاسم أو الكود أو المدير...">
            </div>
            <div class="col-md-3">
                <label class="form-label">نوع المخزن</label>
                <select class="form-select" name="type">
                    <option value="">جميع الأنواع</option>
                    {% for value, label in warehouse_types %}
                        <option value="{{ value }}" {% if warehouse_type == value %}selected{% endif %}>
                            {{ label }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="bi bi-search me-1"></i>بحث
                </button>
                <a href="{% url 'definitions:warehouse_list' %}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-clockwise me-1"></i>إعادة تعيين
                </a>
            </div>
        </form>
    </div>

    <!-- Warehouses List -->
    <div class="row">
        {% for warehouse in page_obj %}
        <div class="col-lg-6 col-xl-4">
            <div class="warehouse-card">
                <div class="warehouse-card-header">
                    <div>
                        <h5 class="mb-0">{{ warehouse.name }}</h5>
                        <small>{{ warehouse.code }}</small>
                    </div>
                    <span class="warehouse-type-badge">
                        {{ warehouse.get_warehouse_type_display }}
                    </span>
                </div>
                <div class="warehouse-card-body">
                    <div class="warehouse-info">
                        {% if warehouse.address %}
                        <div class="info-item">
                            <i class="bi bi-geo-alt"></i>
                            <span>{{ warehouse.address|truncatechars:30 }}</span>
                        </div>
                        {% endif %}
                        {% if warehouse.phone %}
                        <div class="info-item">
                            <i class="bi bi-telephone"></i>
                            <span>{{ warehouse.phone }}</span>
                        </div>
                        {% endif %}
                        {% if warehouse.manager_name %}
                        <div class="info-item">
                            <i class="bi bi-person"></i>
                            <span>{{ warehouse.manager_name }}</span>
                        </div>
                        {% endif %}
                        <div class="info-item">
                            <i class="bi bi-calendar"></i>
                            <span>{{ warehouse.created_at|date:"d/m/Y" }}</span>
                        </div>
                        <div class="info-item">
                            <i class="bi bi-{{ warehouse.is_active|yesno:'check-circle,x-circle' }}"></i>
                            <span>{{ warehouse.is_active|yesno:"نشط,غير نشط" }}</span>
                        </div>
                        <div class="info-item">
                            <i class="bi bi-{{ warehouse.allow_negative_stock|yesno:'check,x' }}"></i>
                            <span>{{ warehouse.allow_negative_stock|yesno:"يسمح بالسالب,لا يسمح بالسالب" }}</span>
                        </div>
                    </div>
                    <div class="warehouse-actions">
                        <a href="#" class="btn-action btn-view">
                            <i class="bi bi-eye"></i>عرض
                        </a>
                        <a href="#" class="btn-action btn-edit">
                            <i class="bi bi-pencil"></i>تعديل
                        </a>
                        <a href="#" class="btn-action btn-delete" onclick="return confirm('هل أنت متأكد من حذف هذا المخزن؟')">
                            <i class="bi bi-trash"></i>حذف
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="bi bi-building" style="font-size: 4rem; color: #ddd;"></i>
                <h4 class="mt-3 text-muted">لا توجد مخازن</h4>
                <p class="text-muted">لم يتم العثور على أي مخازن تطابق معايير البحث</p>
                <a href="#" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>إضافة مخزن جديد
                </a>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <div class="pagination-wrapper">
        <nav aria-label="تنقل الصفحات">
            <ul class="pagination">
                {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if warehouse_type %}&type={{ warehouse_type }}{% endif %}">الأولى</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if warehouse_type %}&type={{ warehouse_type }}{% endif %}">السابقة</a>
                    </li>
                {% endif %}

                <li class="page-item active">
                    <span class="page-link">{{ page_obj.number }} من {{ page_obj.paginator.num_pages }}</span>
                </li>

                {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if warehouse_type %}&type={{ warehouse_type }}{% endif %}">التالية</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if warehouse_type %}&type={{ warehouse_type }}{% endif %}">الأخيرة</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
    </div>
    {% endif %}
</div>
{% endblock %}
