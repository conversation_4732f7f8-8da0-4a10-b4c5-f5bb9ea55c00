from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Sum
from .models import Asset, AssetCategory, AssetMaintenance

@login_required
def assets_dashboard(request):
    """لوحة تحكم الأصول الثابتة"""
    total_assets = Asset.objects.filter(is_active=True).count()
    total_value = Asset.objects.filter(is_active=True).aggregate(
        total=Sum('purchase_price'))['total'] or 0
    total_depreciation = Asset.objects.filter(is_active=True).aggregate(
        total=Sum('accumulated_depreciation'))['total'] or 0
    maintenance_due = Asset.objects.filter(
        next_maintenance_date__isnull=False,
        status='active'
    ).count()

    context = {
        'total_assets': total_assets,
        'total_value': total_value,
        'total_depreciation': total_depreciation,
        'book_value': total_value - total_depreciation,
        'maintenance_due': maintenance_due,
        'categories_count': AssetCategory.objects.filter(is_active=True).count(),
    }
    return render(request, 'assets/dashboard.html', context)

@login_required
def asset_list(request):
    search_query = request.GET.get('search', '')
    category_filter = request.GET.get('category', '')
    status_filter = request.GET.get('status', '')

    assets = Asset.objects.all()

    if search_query:
        assets = assets.filter(
            Q(name__icontains=search_query) |
            Q(code__icontains=search_query) |
            Q(serial_number__icontains=search_query)
        )

    if category_filter:
        assets = assets.filter(category_id=category_filter)

    if status_filter:
        assets = assets.filter(status=status_filter)

    assets = assets.order_by('-created_at')

    paginator = Paginator(assets, 12)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    categories = AssetCategory.objects.filter(is_active=True)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'category_filter': category_filter,
        'status_filter': status_filter,
        'categories': categories,
        'total_assets': Asset.objects.count()
    }
    return render(request, 'assets/asset_list.html', context)
