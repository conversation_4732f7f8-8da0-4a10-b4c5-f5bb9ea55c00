from django.db import models
from products.models import Product
from definitions.models import Currency, TaxRate

class Supplier(models.Model):
    """الموردين"""
    name = models.CharField(max_length=200, verbose_name="اسم المورد")
    code = models.Char<PERSON>ield(max_length=20, unique=True, verbose_name="رمز المورد")
    contact_person = models.CharField(max_length=100, blank=True, verbose_name="الشخص المسؤول")
    phone = models.CharField(max_length=20, verbose_name="رقم الهاتف")
    email = models.EmailField(blank=True, verbose_name="البريد الإلكتروني")
    address = models.TextField(verbose_name="العنوان")
    tax_number = models.CharField(max_length=50, blank=True, verbose_name="الرقم الضريبي")
    payment_terms = models.CharField(max_length=100, blank=True, verbose_name="شروط الدفع")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "مورد"
        verbose_name_plural = "الموردين"
        ordering = ['name']

    def __str__(self):
        return self.name

class PurchaseOrder(models.Model):
    """أوامر الشراء"""
    STATUS_CHOICES = [
        ('draft', 'مسودة'),
        ('sent', 'مرسل'),
        ('confirmed', 'مؤكد'),
        ('received', 'تم الاستلام'),
        ('cancelled', 'ملغي'),
    ]

    order_number = models.CharField(max_length=50, unique=True, verbose_name="رقم أمر الشراء")
    supplier = models.ForeignKey(Supplier, on_delete=models.CASCADE, verbose_name="المورد")
    order_date = models.DateField(verbose_name="تاريخ الطلب")
    expected_delivery_date = models.DateField(null=True, blank=True, verbose_name="تاريخ التسليم المتوقع")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft', verbose_name="الحالة")
    currency = models.ForeignKey(Currency, on_delete=models.CASCADE, verbose_name="العملة")
    subtotal = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="المجموع الفرعي")
    tax_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="مبلغ الضريبة")
    total_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="المجموع الكلي")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "أمر شراء"
        verbose_name_plural = "أوامر الشراء"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.order_number} - {self.supplier.name}"

class PurchaseOrderItem(models.Model):
    """عناصر أمر الشراء"""
    order = models.ForeignKey(PurchaseOrder, on_delete=models.CASCADE, related_name='items', verbose_name="أمر الشراء")
    product = models.ForeignKey(Product, on_delete=models.CASCADE, verbose_name="المنتج")
    quantity = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="الكمية")
    unit_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="سعر الوحدة")
    discount_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0, verbose_name="نسبة الخصم %")
    discount_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name="مبلغ الخصم")
    tax_rate = models.ForeignKey(TaxRate, on_delete=models.CASCADE, null=True, blank=True, verbose_name="معدل الضريبة")
    line_total = models.DecimalField(max_digits=15, decimal_places=2, verbose_name="إجمالي السطر")
    received_quantity = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name="الكمية المستلمة")

    class Meta:
        verbose_name = "عنصر أمر شراء"
        verbose_name_plural = "عناصر أوامر الشراء"

    def __str__(self):
        return f"{self.product.name} - {self.quantity}"
