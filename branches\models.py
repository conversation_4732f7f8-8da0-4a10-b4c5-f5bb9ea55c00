from django.db import models

class Branch(models.Model):
    """الفروع والمراكز"""
    BRANCH_TYPES = [
        ('headquarters', 'المركز الرئيسي'),
        ('branch', 'فرع'),
        ('warehouse', 'مستودع'),
        ('office', 'مكتب'),
    ]

    name = models.CharField(max_length=200, verbose_name="اسم الفرع")
    code = models.CharField(max_length=20, unique=True, verbose_name="رمز الفرع")
    branch_type = models.CharField(max_length=20, choices=BRANCH_TYPES, verbose_name="نوع الفرع")
    address = models.TextField(verbose_name="العنوان")
    city = models.CharField(max_length=100, verbose_name="المدينة")
    country = models.CharField(max_length=100, default="السعودية", verbose_name="الدولة")
    phone = models.Cha<PERSON><PERSON><PERSON>(max_length=20, blank=True, verbose_name="رقم الهاتف")
    email = models.EmailField(blank=True, verbose_name="البريد الإلكتروني")
    manager_name = models.CharField(max_length=100, blank=True, verbose_name="اسم المدير")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "فرع"
        verbose_name_plural = "الفروع"
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.code})"
