from django.contrib import admin
from .models import (
    WarehouseDefinition, ProductCategory, ProductDefinition, ProductLocation,
    ProductCode, BankDefinition, CurrencyDefinition, CashBoxDefinition,
    AssetGroup, PersonDefinition, ExpenseType, ExpenseName,
    RevenueType, RevenueName, ProfitCenter, PrinterDefinition
)

@admin.register(CurrencyDefinition)
class CurrencyDefinitionAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'symbol', 'exchange_rate', 'is_base_currency', 'is_active']
    list_filter = ['is_base_currency', 'is_active', 'created_at']
    search_fields = ['code', 'name', 'name_en']
    readonly_fields = ['created_at', 'updated_at']

@admin.register(WarehouseDefinition)
class WarehouseDefinitionAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'warehouse_type', 'manager_name', 'is_active']
    list_filter = ['warehouse_type', 'is_active', 'allow_negative_stock', 'auto_reorder']
    search_fields = ['code', 'name', 'name_en', 'manager_name']
    readonly_fields = ['created_at', 'updated_at']

@admin.register(ProductCategory)
class ProductCategoryAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'parent', 'sort_order', 'is_active']
    list_filter = ['is_active', 'created_at']
    search_fields = ['code', 'name', 'name_en']
    readonly_fields = ['created_at', 'updated_at']

@admin.register(ProductDefinition)
class ProductDefinitionAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'category', 'product_type', 'cost_price', 'selling_price', 'is_active']
    list_filter = ['product_type', 'category', 'is_active', 'is_sellable', 'is_purchasable', 'track_inventory']
    search_fields = ['code', 'name', 'name_en', 'barcode']
    readonly_fields = ['created_at', 'updated_at']

@admin.register(BankDefinition)
class BankDefinitionAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'swift_code', 'contact_person', 'is_active']
    list_filter = ['is_active', 'created_at']
    search_fields = ['code', 'name', 'name_en', 'swift_code']
    readonly_fields = ['created_at', 'updated_at']

@admin.register(CashBoxDefinition)
class CashBoxDefinitionAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'currency', 'current_balance', 'responsible_person', 'is_active']
    list_filter = ['currency', 'is_active', 'allow_negative_balance']
    search_fields = ['code', 'name', 'name_en']
    readonly_fields = ['created_at', 'updated_at']

@admin.register(AssetGroup)
class AssetGroupAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'asset_type', 'depreciation_rate', 'useful_life_years', 'is_active']
    list_filter = ['asset_type', 'depreciation_method', 'is_active']
    search_fields = ['code', 'name', 'name_en']
    readonly_fields = ['created_at', 'updated_at']

@admin.register(PersonDefinition)
class PersonDefinitionAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'person_type', 'phone', 'email', 'is_active']
    list_filter = ['person_type', 'gender', 'is_active', 'city', 'country']
    search_fields = ['code', 'name', 'name_en', 'phone', 'mobile', 'email']
    readonly_fields = ['created_at', 'updated_at']

@admin.register(ExpenseType)
class ExpenseTypeAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'parent', 'is_active']
    list_filter = ['is_active', 'created_at']
    search_fields = ['code', 'name', 'name_en']

@admin.register(ExpenseName)
class ExpenseNameAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'expense_type', 'default_amount', 'is_recurring', 'is_active']
    list_filter = ['expense_type', 'is_recurring', 'recurrence_period', 'is_active']
    search_fields = ['code', 'name', 'name_en']

@admin.register(RevenueType)
class RevenueTypeAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'parent', 'is_active']
    list_filter = ['is_active', 'created_at']
    search_fields = ['code', 'name', 'name_en']

@admin.register(RevenueName)
class RevenueNameAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'revenue_type', 'default_amount', 'is_recurring', 'is_active']
    list_filter = ['revenue_type', 'is_recurring', 'recurrence_period', 'is_active']
    search_fields = ['code', 'name', 'name_en']

@admin.register(ProfitCenter)
class ProfitCenterAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'manager', 'target_revenue', 'target_profit', 'is_active']
    list_filter = ['is_active', 'created_at']
    search_fields = ['code', 'name', 'name_en']
    readonly_fields = ['created_at', 'updated_at']

@admin.register(PrinterDefinition)
class PrinterDefinitionAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'printer_type', 'connection_type', 'is_active']
    list_filter = ['printer_type', 'connection_type', 'is_active', 'default_for_receipts', 'default_for_reports']
    search_fields = ['code', 'name']
    readonly_fields = ['created_at', 'updated_at']

@admin.register(ProductLocation)
class ProductLocationAdmin(admin.ModelAdmin):
    list_display = ['warehouse', 'product', 'location_code', 'is_default', 'is_active']
    list_filter = ['warehouse', 'is_default', 'is_active']
    search_fields = ['product__name', 'product__code', 'location_code']

@admin.register(ProductCode)
class ProductCodeAdmin(admin.ModelAdmin):
    list_display = ['product', 'code_type', 'code', 'supplier_name', 'is_active']
    list_filter = ['code_type', 'is_active']
    search_fields = ['product__name', 'product__code', 'code', 'supplier_name']
