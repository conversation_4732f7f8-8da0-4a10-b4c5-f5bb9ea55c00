from django import forms
from .models import Bank, BankAccount, BankTransaction

class BankForm(forms.ModelForm):
    class Meta:
        model = Bank
        fields = ['name', 'code', 'swift_code', 'address', 'phone', 'email', 'website', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم البنك'}),
            'code': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رمز البنك'}),
            'swift_code': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رمز SWIFT'}),
            'address': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'عنوان البنك'}),
            'phone': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رقم الهاتف'}),
            'email': forms.EmailInput(attrs={'class': 'form-control', 'placeholder': 'البريد الإلكتروني'}),
            'website': forms.URLInput(attrs={'class': 'form-control', 'placeholder': 'الموقع الإلكتروني'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
        labels = {
            'name': 'اسم البنك',
            'code': 'رمز البنك',
            'swift_code': 'رمز SWIFT',
            'address': 'العنوان',
            'phone': 'رقم الهاتف',
            'email': 'البريد الإلكتروني',
            'website': 'الموقع الإلكتروني',
            'is_active': 'نشط',
        }

class BankAccountForm(forms.ModelForm):
    class Meta:
        model = BankAccount
        fields = ['bank', 'account_name', 'account_number', 'iban', 'account_type', 
                 'currency', 'opening_balance', 'opening_date', 'notes', 'is_active']
        widgets = {
            'bank': forms.Select(attrs={'class': 'form-select'}),
            'account_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم الحساب'}),
            'account_number': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رقم الحساب'}),
            'iban': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رقم IBAN'}),
            'account_type': forms.Select(attrs={'class': 'form-select'}),
            'currency': forms.Select(attrs={'class': 'form-select'}),
            'opening_balance': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'opening_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'ملاحظات'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
        labels = {
            'bank': 'البنك',
            'account_name': 'اسم الحساب',
            'account_number': 'رقم الحساب',
            'iban': 'رقم IBAN',
            'account_type': 'نوع الحساب',
            'currency': 'العملة',
            'opening_balance': 'الرصيد الافتتاحي',
            'opening_date': 'تاريخ فتح الحساب',
            'notes': 'ملاحظات',
            'is_active': 'نشط',
        }

class BankTransactionForm(forms.ModelForm):
    class Meta:
        model = BankTransaction
        fields = ['account', 'transaction_type', 'amount', 'description', 
                 'reference_number', 'transaction_date']
        widgets = {
            'account': forms.Select(attrs={'class': 'form-select'}),
            'transaction_type': forms.Select(attrs={'class': 'form-select'}),
            'amount': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
            'description': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'وصف المعاملة'}),
            'reference_number': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رقم المرجع'}),
            'transaction_date': forms.DateTimeInput(attrs={'class': 'form-control', 'type': 'datetime-local'}),
        }
        labels = {
            'account': 'الحساب البنكي',
            'transaction_type': 'نوع المعاملة',
            'amount': 'المبلغ',
            'description': 'الوصف',
            'reference_number': 'رقم المرجع',
            'transaction_date': 'تاريخ المعاملة',
        }
