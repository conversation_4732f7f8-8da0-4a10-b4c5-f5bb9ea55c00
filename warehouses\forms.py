from django import forms
from .models import (
    Warehouse, WarehouseZone, WarehouseLocation, Stock, 
    StockMovement, StockTransfer, StockTransferItem,
    StockAdjustment, StockAdjustmentItem
)

class WarehouseForm(forms.ModelForm):
    class Meta:
        model = Warehouse
        fields = ['name', 'code', 'warehouse_type', 'branch', 'address', 'capacity', 
                 'manager_name', 'phone', 'email', 'notes', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم المخزن'}),
            'code': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رمز المخزن'}),
            'warehouse_type': forms.Select(attrs={'class': 'form-select'}),
            'branch': forms.Select(attrs={'class': 'form-select'}),
            'address': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'عنوان المخزن'}),
            'capacity': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
            'manager_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم المسؤول'}),
            'phone': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رقم الهاتف'}),
            'email': forms.EmailInput(attrs={'class': 'form-control', 'placeholder': 'البريد الإلكتروني'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'ملاحظات'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
        labels = {
            'name': 'اسم المخزن',
            'code': 'رمز المخزن',
            'warehouse_type': 'نوع المخزن',
            'branch': 'الفرع',
            'address': 'العنوان',
            'capacity': 'السعة (متر مكعب)',
            'manager_name': 'اسم المسؤول',
            'phone': 'رقم الهاتف',
            'email': 'البريد الإلكتروني',
            'notes': 'ملاحظات',
            'is_active': 'نشط',
        }

class WarehouseZoneForm(forms.ModelForm):
    class Meta:
        model = WarehouseZone
        fields = ['warehouse', 'name', 'code', 'description', 'temperature_controlled', 'is_active']
        widgets = {
            'warehouse': forms.Select(attrs={'class': 'form-select'}),
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم المنطقة'}),
            'code': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رمز المنطقة'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'وصف المنطقة'}),
            'temperature_controlled': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
        labels = {
            'warehouse': 'المخزن',
            'name': 'اسم المنطقة',
            'code': 'رمز المنطقة',
            'description': 'الوصف',
            'temperature_controlled': 'مكيف الهواء',
            'is_active': 'نشط',
        }

class WarehouseLocationForm(forms.ModelForm):
    class Meta:
        model = WarehouseLocation
        fields = ['zone', 'aisle', 'rack', 'shelf', 'bin', 'barcode', 'capacity', 'is_active']
        widgets = {
            'zone': forms.Select(attrs={'class': 'form-select'}),
            'aisle': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'الممر'}),
            'rack': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'الرف'}),
            'shelf': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'الطبقة'}),
            'bin': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'الصندوق'}),
            'barcode': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'الباركود'}),
            'capacity': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
        labels = {
            'zone': 'المنطقة',
            'aisle': 'الممر',
            'rack': 'الرف',
            'shelf': 'الطبقة',
            'bin': 'الصندوق',
            'barcode': 'الباركود',
            'capacity': 'السعة',
            'is_active': 'نشط',
        }

class StockForm(forms.ModelForm):
    class Meta:
        model = Stock
        fields = ['product', 'warehouse', 'location', 'quantity', 'minimum_stock', 
                 'maximum_stock', 'reorder_point']
        widgets = {
            'product': forms.Select(attrs={'class': 'form-select'}),
            'warehouse': forms.Select(attrs={'class': 'form-select'}),
            'location': forms.Select(attrs={'class': 'form-select'}),
            'quantity': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
            'minimum_stock': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
            'maximum_stock': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
            'reorder_point': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
        }
        labels = {
            'product': 'المنتج',
            'warehouse': 'المخزن',
            'location': 'موقع التخزين',
            'quantity': 'الكمية',
            'minimum_stock': 'الحد الأدنى للمخزون',
            'maximum_stock': 'الحد الأقصى للمخزون',
            'reorder_point': 'نقطة إعادة الطلب',
        }

class StockMovementForm(forms.ModelForm):
    class Meta:
        model = StockMovement
        fields = ['product', 'warehouse', 'location', 'movement_type', 'reference_type',
                 'reference_number', 'quantity', 'unit_cost', 'notes']
        widgets = {
            'product': forms.Select(attrs={'class': 'form-select'}),
            'warehouse': forms.Select(attrs={'class': 'form-select'}),
            'location': forms.Select(attrs={'class': 'form-select'}),
            'movement_type': forms.Select(attrs={'class': 'form-select'}),
            'reference_type': forms.Select(attrs={'class': 'form-select'}),
            'reference_number': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رقم المرجع'}),
            'quantity': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
            'unit_cost': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'ملاحظات'}),
        }
        labels = {
            'product': 'المنتج',
            'warehouse': 'المخزن',
            'location': 'موقع التخزين',
            'movement_type': 'نوع الحركة',
            'reference_type': 'نوع المرجع',
            'reference_number': 'رقم المرجع',
            'quantity': 'الكمية',
            'unit_cost': 'تكلفة الوحدة',
            'notes': 'ملاحظات',
        }

class StockTransferForm(forms.ModelForm):
    class Meta:
        model = StockTransfer
        fields = ['transfer_number', 'from_warehouse', 'to_warehouse', 'transfer_date',
                 'expected_arrival', 'notes']
        widgets = {
            'transfer_number': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رقم التحويل'}),
            'from_warehouse': forms.Select(attrs={'class': 'form-select'}),
            'to_warehouse': forms.Select(attrs={'class': 'form-select'}),
            'transfer_date': forms.DateTimeInput(attrs={'class': 'form-control', 'type': 'datetime-local'}),
            'expected_arrival': forms.DateTimeInput(attrs={'class': 'form-control', 'type': 'datetime-local'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'ملاحظات'}),
        }
        labels = {
            'transfer_number': 'رقم التحويل',
            'from_warehouse': 'من المخزن',
            'to_warehouse': 'إلى المخزن',
            'transfer_date': 'تاريخ التحويل',
            'expected_arrival': 'تاريخ الوصول المتوقع',
            'notes': 'ملاحظات',
        }

class StockAdjustmentForm(forms.ModelForm):
    class Meta:
        model = StockAdjustment
        fields = ['adjustment_number', 'warehouse', 'adjustment_type', 'adjustment_date',
                 'reason', 'approved_by']
        widgets = {
            'adjustment_number': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رقم التسوية'}),
            'warehouse': forms.Select(attrs={'class': 'form-select'}),
            'adjustment_type': forms.Select(attrs={'class': 'form-select'}),
            'adjustment_date': forms.DateTimeInput(attrs={'class': 'form-control', 'type': 'datetime-local'}),
            'reason': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'سبب التسوية'}),
            'approved_by': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'معتمد من'}),
        }
        labels = {
            'adjustment_number': 'رقم التسوية',
            'warehouse': 'المخزن',
            'adjustment_type': 'نوع التسوية',
            'adjustment_date': 'تاريخ التسوية',
            'reason': 'سبب التسوية',
            'approved_by': 'معتمد من',
        }
