{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - أوساريك</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <style>
        body { background: #f4f6fb; font-family: 'Cairo', Tahoma, Arial, sans-serif; }
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(180deg, #0d6efd 70%, #0dcaf0 100%);
            color: #fff;
            padding-top: 2rem;
        }
        .sidebar .nav-link {
            color: #fff;
            font-weight: 600;
            border-radius: 0.75rem;
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
            letter-spacing: 0.5px;
            transition: background 0.2s, color 0.2s, box-shadow 0.2s;
        }
        .sidebar .nav-link.active, .sidebar .nav-link:hover {
            background: #fff;
            color: #0d6efd;
            box-shadow: 0 2px 12px #0d6efd22;
        }
        .sidebar .nav-link i {
            margin-left: 0.5rem;
        }
        .dashboard-header {
            background: linear-gradient(90deg, #0d6efd 60%, #0dcaf0 100%);
            color: #fff;
            border-radius: 1rem 1rem 0 0;
            padding: 2rem 1rem 1rem 1rem;
            font-family: 'Cairo', Tahoma, Arial, sans-serif;
        }
        .dashboard-tile {
            transition: box-shadow 0.2s, transform 0.2s;
            cursor: pointer;
            border: none;
        }
        .dashboard-tile:hover {
            box-shadow: 0 0 20px #0d6efd33;
            transform: translateY(-5px) scale(1.03);
        }
        .dashboard-icon {
            font-size: 2.5rem;
            color: #0d6efd;
        }
        .card-title, h5, h1, .fw-bold { font-family: 'Cairo', Tahoma, Arial, sans-serif; }
        .card {
            border-radius: 1rem;
        }
        .bg-light {
            background: #e9f1fb !important;
        }
        .text-muted { color: #6c757d !important; }
        .topbar {
            background: #fff;
            border-bottom: 1px solid #e3e6f0;
            padding: 0.75rem 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        .topbar .datetime {
            font-size: 1.1rem;
            color: #0d6efd;
            font-weight: 700;
            letter-spacing: 1px;
        }
        .topbar .search-box {
            width: 300px;
            max-width: 100%;
        }
        .topbar .user {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 600;
            color: #0d6efd;
        }
        .topbar .user i {
            font-size: 1.5rem;
        }
        @media (max-width: 768px) {
            .topbar { flex-direction: column; gap: 0.5rem; align-items: stretch; }
            .topbar .search-box { width: 100%; }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row flex-nowrap">
            <div class="col-auto sidebar d-flex flex-column align-items-start shadow">
                <a class="navbar-brand fw-bold mb-4 fs-3" href="#">أوساريك</a>
                <ul class="nav nav-pills flex-column w-100">
                    <li class="nav-item mb-2"><a class="nav-link active" href="/"><i class="bi bi-house-door"></i>الرئيسية</a></li>

                    <!-- التعريفات -->
                    <li class="nav-item mb-2"><a class="nav-link" href="/definitions/"><i class="bi bi-gear"></i>التعريفات</a></li>

                    <!-- المخازن -->
                    <li class="nav-item mb-2"><a class="nav-link" href="/warehouses/"><i class="bi bi-building"></i>المخازن</a></li>

                    <!-- التصنيع -->
                    <li class="nav-item mb-2"><a class="nav-link" href="/manufacturing/"><i class="bi bi-gear-wide-connected"></i>التصنيع</a></li>

                    <!-- البنوك -->
                    <li class="nav-item mb-2"><a class="nav-link" href="#"><i class="bi bi-bank"></i>البنوك <small class="text-muted">(قريباً)</small></a></li>

                    <!-- الأصول الثابتة -->
                    <li class="nav-item mb-2"><a class="nav-link" href="#"><i class="bi bi-building-gear"></i>الأصول الثابتة <small class="text-muted">(قريباً)</small></a></li>

                    <!-- الفروع والمراكز -->
                    <li class="nav-item mb-2"><a class="nav-link" href="#"><i class="bi bi-geo-alt"></i>الفروع والمراكز <small class="text-muted">(قريباً)</small></a></li>

                    <!-- دليل الحسابات -->
                    <li class="nav-item mb-2"><a class="nav-link" href="#"><i class="bi bi-journal-text"></i>دليل الحسابات <small class="text-muted">(قريباً)</small></a></li>

                    <!-- شؤون العاملين -->
                    <li class="nav-item mb-2"><a class="nav-link" href="#"><i class="bi bi-people"></i>شؤون العاملين <small class="text-muted">(قريباً)</small></a></li>

                    <!-- المبيعات -->
                    <li class="nav-item mb-2"><a class="nav-link" href="#"><i class="bi bi-cart-check"></i>المبيعات <small class="text-muted">(قريباً)</small></a></li>

                    <!-- المشتريات -->
                    <li class="nav-item mb-2"><a class="nav-link" href="#"><i class="bi bi-bag-plus"></i>المشتريات <small class="text-muted">(قريباً)</small></a></li>

                    <!-- التقارير -->
                    <li class="nav-item mb-2"><a class="nav-link" href="#"><i class="bi bi-graph-up"></i>التقارير <small class="text-muted">(قريباً)</small></a></li>
                </ul>
            </div>
            <div class="col px-0">
                <div class="topbar mb-3">
                    <div class="datetime" id="datetime"></div>
                    <form class="search-box d-flex" role="search">
                        <input class="form-control me-2" type="search" placeholder="بحث..." aria-label="بحث">
                        <button class="btn btn-outline-primary" type="submit"><i class="bi bi-search"></i></button>
                    </form>
                    <div class="user">
                        <i class="bi bi-person-circle"></i>
                        <span>{{ user.get_full_name|default:user.username }}</span>
                    </div>
                </div>
                <div class="dashboard-header text-center mb-4 shadow">
                    <h1 class="display-5 fw-bold mb-2">لوحة التحكم الرئيسية</h1>
                    <p class="lead mb-0">مرحباً بك في نظام أوساريك لإدارة الحسابات والمخزون</p>
                </div>
                <div class="row g-4 mb-4">
                    <div class="col-md-3 col-6">
                        <div class="dashboard-tile bg-white rounded shadow-sm p-4 text-center">
                            <i class="bi bi-bar-chart-line dashboard-icon mb-2"></i>
                            <h5 class="fw-bold">المبيعات</h5>
                            <p class="text-muted small mb-0">عرض تقارير المبيعات والفواتير</p>
                        </div>
                    </div>
                    <div class="col-md-3 col-6">
                        <div class="dashboard-tile bg-white rounded shadow-sm p-4 text-center">
                            <i class="bi bi-cart-check dashboard-icon mb-2"></i>
                            <h5 class="fw-bold">المشتريات</h5>
                            <p class="text-muted small mb-0">إدارة المشتريات والموردين</p>
                        </div>
                    </div>
                    <div class="col-md-3 col-6">
                        <div class="dashboard-tile bg-white rounded shadow-sm p-4 text-center">
                            <i class="bi bi-box-seam dashboard-icon mb-2"></i>
                            <h5 class="fw-bold">المخزون</h5>
                            <p class="text-muted small mb-0">تتبع المنتجات والكميات</p>
                        </div>
                    </div>
                    <div class="col-md-3 col-6">
                        <div class="dashboard-tile bg-white rounded shadow-sm p-4 text-center">
                            <i class="bi bi-people dashboard-icon mb-2"></i>
                            <h5 class="fw-bold">العملاء والموردين</h5>
                            <p class="text-muted small mb-0">إدارة بيانات العملاء والموردين</p>
                        </div>
                    </div>
                </div>
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card shadow-sm">
                            <div class="card-body">
                                <h5 class="card-title mb-3">ملخص سريع</h5>
                                <div class="row text-center">
                                    <div class="col-md-3 col-6 mb-3">
                                        <div class="p-3 bg-light rounded">عدد الفواتير: <span class="fw-bold">{{ invoices_count }}</span></div>
                                    </div>
                                    <div class="col-md-3 col-6 mb-3">
                                        <div class="p-3 bg-light rounded">عدد العملاء: <span class="fw-bold">{{ customers_count }}</span></div>
                                    </div>
                                    <div class="col-md-3 col-6 mb-3">
                                        <div class="p-3 bg-light rounded">عدد المنتجات: <span class="fw-bold">{{ products_count }}</span></div>
                                    </div>
                                    <div class="col-md-3 col-6 mb-3">
                                        <div class="p-3 bg-light rounded">تنبيهات المخزون: <span class="fw-bold text-danger">{{ low_stock_count }}</span></div>
                                    </div>
                                </div>
                                <hr>
                                <div class="row">
                                    <div class="col-12">
                                        <canvas id="mainChart" height="80"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.0/dist/chart.umd.min.js"></script>
    <script>
        // رسم بياني تجريبي
        const ctx = document.getElementById('mainChart').getContext('2d');
        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                datasets: [{
                    label: 'المبيعات',
                    data: [12, 19, 3, 5, 2, 3],
                    backgroundColor: '#0d6efd',
                }, {
                    label: 'المشتريات',
                    data: [8, 11, 7, 6, 4, 5],
                    backgroundColor: '#0dcaf0',
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: { position: 'top', labels: { font: { family: 'Cairo, sans-serif' } } },
                    title: { display: true, text: 'إحصائيات المبيعات والمشتريات' }
                },
                scales: {
                    x: { title: { display: true, text: 'الشهر' } },
                    y: { title: { display: true, text: 'القيمة' } }
                }
            }
        });
        // تحديث الوقت والتاريخ في الشريط العلوي
        function updateDateTime() {
            const now = new Date();
            const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
            const dateStr = now.toLocaleDateString('ar-EG', options);
            const timeStr = now.toLocaleTimeString('ar-EG', { hour: '2-digit', minute: '2-digit', second: '2-digit' });
            document.getElementById('datetime').textContent = `${dateStr} - ${timeStr}`;
        }
        setInterval(updateDateTime, 1000);
        updateDateTime();
    </script>
</body>
</html>
