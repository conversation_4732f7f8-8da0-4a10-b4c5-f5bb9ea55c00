{% extends 'base.html' %}

{% block title %}لوحة التحكم - أوساريك{% endblock %}

{% block content %}
    <div class="page-header">
        <h1 class="page-title">لوحة التحكم الرئيسية</h1>
        <p class="page-subtitle">مرحباً بك في نظام أوساريك لإدارة الحسابات والمخزون</p>
    </div>

    <!-- Dashboard Cards -->
    <div class="row g-4 mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="card text-center">
                <div class="card-body">
                    <i class="bi bi-receipt text-primary" style="font-size: 3rem;"></i>
                    <h4 class="mt-3 mb-1">{{ invoices_count }}</h4>
                    <p class="text-muted mb-0">إجمالي الفواتير</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card text-center">
                <div class="card-body">
                    <i class="bi bi-people text-success" style="font-size: 3rem;"></i>
                    <h4 class="mt-3 mb-1">{{ customers_count }}</h4>
                    <p class="text-muted mb-0">عدد العملاء</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card text-center">
                <div class="card-body">
                    <i class="bi bi-box-seam text-info" style="font-size: 3rem;"></i>
                    <h4 class="mt-3 mb-1">{{ products_count }}</h4>
                    <p class="text-muted mb-0">عدد المنتجات</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card text-center">
                <div class="card-body">
                    <i class="bi bi-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                    <h4 class="mt-3 mb-1 text-danger">{{ low_stock_count }}</h4>
                    <p class="text-muted mb-0">تنبيهات المخزون</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row g-4 mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="bi bi-plus-circle text-primary" style="font-size: 2.5rem;"></i>
                    <h5 class="mt-3 mb-2">إضافة فاتورة جديدة</h5>
                    <p class="text-muted small mb-3">إنشاء فاتورة مبيعات جديدة</p>
                    <a href="#" class="btn btn-primary btn-sm">إضافة فاتورة</a>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="bi bi-person-plus text-success" style="font-size: 2.5rem;"></i>
                    <h5 class="mt-3 mb-2">إضافة عميل جديد</h5>
                    <p class="text-muted small mb-3">تسجيل عميل جديد في النظام</p>
                    <a href="{% url 'customers:customer_create' %}" class="btn btn-success btn-sm">إضافة عميل</a>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="bi bi-box text-info" style="font-size: 2.5rem;"></i>
                    <h5 class="mt-3 mb-2">إضافة منتج جديد</h5>
                    <p class="text-muted small mb-3">إضافة منتج جديد للمخزون</p>
                    <a href="#" class="btn btn-info btn-sm">إضافة منتج</a>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="bi bi-graph-up text-warning" style="font-size: 2.5rem;"></i>
                    <h5 class="mt-3 mb-2">عرض التقارير</h5>
                    <p class="text-muted small mb-3">مراجعة التقارير والإحصائيات</p>
                    <a href="#" class="btn btn-warning btn-sm">عرض التقارير</a>
                </div>
            </div>
        </div>
    </div>
    <!-- Charts and Analytics -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">إحصائيات المبيعات والمشتريات</h5>
                </div>
                <div class="card-body">
                    <canvas id="salesChart" height="100"></canvas>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">أحدث الأنشطة</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-shrink-0">
                            <i class="bi bi-receipt text-primary"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-1">فاتورة جديدة</h6>
                            <p class="text-muted small mb-0">تم إنشاء فاتورة رقم #1001</p>
                        </div>
                        <small class="text-muted">منذ ساعة</small>
                    </div>
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-shrink-0">
                            <i class="bi bi-person-plus text-success"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-1">عميل جديد</h6>
                            <p class="text-muted small mb-0">تم تسجيل عميل جديد</p>
                        </div>
                        <small class="text-muted">منذ ساعتين</small>
                    </div>
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-shrink-0">
                            <i class="bi bi-box text-warning"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-1">تنبيه مخزون</h6>
                            <p class="text-muted small mb-0">منتج يحتاج إعادة تموين</p>
                        </div>
                        <small class="text-muted">منذ 3 ساعات</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block extra_js %}
<script>
    // Sales Chart
    const ctx = document.getElementById('salesChart').getContext('2d');
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
            datasets: [{
                label: 'المبيعات',
                data: [12000, 19000, 15000, 25000, 22000, 30000],
                borderColor: 'rgb(13, 110, 253)',
                backgroundColor: 'rgba(13, 110, 253, 0.1)',
                tension: 0.4,
                fill: true
            }, {
                label: 'المشتريات',
                data: [8000, 11000, 13000, 16000, 14000, 18000],
                borderColor: 'rgb(13, 202, 240)',
                backgroundColor: 'rgba(13, 202, 240, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        font: {
                            family: 'Cairo, sans-serif'
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value.toLocaleString('ar-EG') + ' ر.س';
                        }
                    }
                }
            }
        }
    });
</script>
{% endblock %}
