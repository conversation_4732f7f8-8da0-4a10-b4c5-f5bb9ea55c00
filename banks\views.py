from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Sum
from .models import Bank, BankAccount, BankTransaction
from .forms import BankForm, BankAccountForm, BankTransactionForm

@login_required
def banks_dashboard(request):
    """لوحة تحكم البنوك"""
    context = {
        'banks_count': Bank.objects.filter(is_active=True).count(),
        'accounts_count': BankAccount.objects.filter(is_active=True).count(),
        'total_balance': BankAccount.objects.filter(is_active=True).aggregate(
            total=Sum('balance'))['total'] or 0,
        'transactions_count': BankTransaction.objects.count(),
    }
    return render(request, 'banks/dashboard.html', context)

# Bank Views
@login_required
def bank_list(request):
    search_query = request.GET.get('search', '')
    banks = Bank.objects.all()

    if search_query:
        banks = banks.filter(
            Q(name__icontains=search_query) |
            Q(code__icontains=search_query)
        )

    banks = banks.order_by('-created_at')

    paginator = Paginator(banks, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'total_banks': Bank.objects.count()
    }
    return render(request, 'banks/bank_list.html', context)

@login_required
def bank_create(request):
    if request.method == 'POST':
        form = BankForm(request.POST)
        if form.is_valid():
            bank = form.save()
            messages.success(request, f'تم إضافة البنك "{bank.name}" بنجاح!')
            return redirect('banks:bank_list')
    else:
        form = BankForm()

    return render(request, 'banks/bank_form.html', {
        'form': form,
        'title': 'إضافة بنك جديد',
        'button_text': 'إضافة البنك'
    })

@login_required
def bank_edit(request, pk):
    bank = get_object_or_404(Bank, pk=pk)

    if request.method == 'POST':
        form = BankForm(request.POST, instance=bank)
        if form.is_valid():
            bank = form.save()
            messages.success(request, f'تم تحديث البنك "{bank.name}" بنجاح!')
            return redirect('banks:bank_list')
    else:
        form = BankForm(instance=bank)

    return render(request, 'banks/bank_form.html', {
        'form': form,
        'bank': bank,
        'title': f'تعديل البنك: {bank.name}',
        'button_text': 'حفظ التغييرات'
    })

@login_required
def bank_delete(request, pk):
    bank = get_object_or_404(Bank, pk=pk)

    if request.method == 'POST':
        bank_name = bank.name
        bank.delete()
        messages.success(request, f'تم حذف البنك "{bank_name}" بنجاح!')
        return redirect('banks:bank_list')

    return render(request, 'banks/bank_confirm_delete.html', {'bank': bank})
