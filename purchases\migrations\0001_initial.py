# Generated by Django 5.2.4 on 2025-07-11 04:51

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('definitions', '0001_initial'),
        ('products', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Supplier',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم المورد')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='رمز المورد')),
                ('contact_person', models.CharField(blank=True, max_length=100, verbose_name='الشخص المسؤول')),
                ('phone', models.CharField(max_length=20, verbose_name='رقم الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='البريد الإلكتروني')),
                ('address', models.TextField(verbose_name='العنوان')),
                ('tax_number', models.CharField(blank=True, max_length=50, verbose_name='الرقم الضريبي')),
                ('payment_terms', models.CharField(blank=True, max_length=100, verbose_name='شروط الدفع')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'مورد',
                'verbose_name_plural': 'الموردين',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='PurchaseOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_number', models.CharField(max_length=50, unique=True, verbose_name='رقم أمر الشراء')),
                ('order_date', models.DateField(verbose_name='تاريخ الطلب')),
                ('expected_delivery_date', models.DateField(blank=True, null=True, verbose_name='تاريخ التسليم المتوقع')),
                ('status', models.CharField(choices=[('draft', 'مسودة'), ('sent', 'مرسل'), ('confirmed', 'مؤكد'), ('received', 'تم الاستلام'), ('cancelled', 'ملغي')], default='draft', max_length=20, verbose_name='الحالة')),
                ('subtotal', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='المجموع الفرعي')),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='مبلغ الضريبة')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='المجموع الكلي')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('currency', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.currency', verbose_name='العملة')),
                ('supplier', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='purchases.supplier', verbose_name='المورد')),
            ],
            options={
                'verbose_name': 'أمر شراء',
                'verbose_name_plural': 'أوامر الشراء',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PurchaseOrderItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الكمية')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='سعر الوحدة')),
                ('discount_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='نسبة الخصم %')),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='مبلغ الخصم')),
                ('line_total', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='إجمالي السطر')),
                ('received_quantity', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الكمية المستلمة')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='purchases.purchaseorder', verbose_name='أمر الشراء')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='products.product', verbose_name='المنتج')),
                ('tax_rate', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='definitions.taxrate', verbose_name='معدل الضريبة')),
            ],
            options={
                'verbose_name': 'عنصر أمر شراء',
                'verbose_name_plural': 'عناصر أوامر الشراء',
            },
        ),
    ]
