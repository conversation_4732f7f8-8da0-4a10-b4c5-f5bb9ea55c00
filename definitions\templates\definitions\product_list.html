{% extends 'base.html' %}
{% load static %}

{% block title %}قائمة الأصناف{% endblock %}

{% block extra_css %}
<style>
    .product-header {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        padding: 2rem 0;
        margin-bottom: 2rem;
        color: white;
        border-radius: 15px;
    }

    .product-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        overflow: hidden;
        margin-bottom: 1.5rem;
        border-left: 4px solid #f093fb;
    }

    .product-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .product-card-body {
        padding: 1.5rem;
    }

    .product-header-info {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1rem;
    }

    .product-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 0.25rem;
    }

    .product-code {
        color: #718096;
        font-size: 0.9rem;
        font-weight: 500;
    }

    .product-image {
        width: 80px;
        height: 80px;
        border-radius: 10px;
        object-fit: cover;
        margin-left: 1rem;
    }

    .no-image {
        width: 80px;
        height: 80px;
        border-radius: 10px;
        background: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #adb5bd;
        margin-left: 1rem;
    }

    .product-meta {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .meta-item {
        display: flex;
        align-items: center;
        color: #666;
        font-size: 0.9rem;
    }

    .meta-item i {
        margin-left: 0.5rem;
        color: #f093fb;
        width: 16px;
    }

    .price-info {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 1rem;
    }

    .price-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 1rem;
    }

    .price-item {
        text-align: center;
    }

    .price-label {
        font-size: 0.8rem;
        color: #666;
        margin-bottom: 0.25rem;
    }

    .price-value {
        font-size: 1.1rem;
        font-weight: 600;
        color: #2d3748;
    }

    .product-type-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 500;
        background: #e3f2fd;
        color: #1976d2;
    }

    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .status-active {
        background: #d4edda;
        color: #155724;
    }

    .status-inactive {
        background: #f8d7da;
        color: #721c24;
    }

    .product-actions {
        display: flex;
        gap: 0.5rem;
        justify-content: flex-end;
    }

    .btn-action {
        padding: 0.4rem 0.8rem;
        border-radius: 6px;
        border: none;
        font-size: 0.8rem;
        font-weight: 500;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
    }

    .btn-view {
        background: #e3f2fd;
        color: #1976d2;
    }

    .btn-edit {
        background: #fff3e0;
        color: #f57c00;
    }

    .btn-delete {
        background: #ffebee;
        color: #d32f2f;
    }

    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .search-filters {
        background: white;
        padding: 1.5rem;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
    }

    .stats-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: white;
        padding: 1.5rem;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        text-align: center;
    }

    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: #f093fb;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        color: #666;
        font-size: 0.9rem;
    }

    .barcode {
        font-family: 'Courier New', monospace;
        background: #f8f9fa;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.8rem;
    }
</style>
{% endblock %}

{% block content %}
<!-- Product Header -->
<div class="product-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-box me-2"></i>
                    إدارة الأصناف
                </h1>
                <p class="mb-0 opacity-75">عرض وإدارة جميع المنتجات والخدمات والمواد الخام</p>
            </div>
            <div class="col-md-4 text-end">
                <a href="#" class="btn btn-light btn-lg">
                    <i class="bi bi-plus-circle me-2"></i>
                    إضافة صنف جديد
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Statistics -->
<div class="container-fluid">
    <div class="stats-row">
        <div class="stat-card">
            <div class="stat-number">{{ total_products }}</div>
            <div class="stat-label">إجمالي الأصناف</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ page_obj.paginator.count }}</div>
            <div class="stat-label">الأصناف المعروضة</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ page_obj.paginator.num_pages }}</div>
            <div class="stat-label">عدد الصفحات</div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="search-filters">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label class="form-label">البحث</label>
                <input type="text" class="form-control" name="search" value="{{ search_query }}" 
                       placeholder="البحث بالاسم أو الكود أو الباركود...">
            </div>
            <div class="col-md-3">
                <label class="form-label">الفئة</label>
                <select class="form-select" name="category">
                    <option value="">جميع الفئات</option>
                    {% for category in categories %}
                        <option value="{{ category.id }}" {% if category_filter == category.id|stringformat:"s" %}selected{% endif %}>
                            {{ category.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">نوع الصنف</label>
                <select class="form-select" name="type">
                    <option value="">جميع الأنواع</option>
                    {% for value, label in product_types %}
                        <option value="{{ value }}" {% if product_type == value %}selected{% endif %}>
                            {{ label }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="bi bi-search me-1"></i>بحث
                </button>
                <a href="{% url 'definitions:product_list' %}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-clockwise me-1"></i>إعادة تعيين
                </a>
            </div>
        </form>
    </div>

    <!-- Products List -->
    <div class="row">
        {% for product in page_obj %}
        <div class="col-lg-6 col-xl-4">
            <div class="product-card">
                <div class="product-card-body">
                    <div class="product-header-info">
                        <div class="flex-grow-1">
                            <div class="product-title">{{ product.name }}</div>
                            <div class="product-code">{{ product.code }}</div>
                            {% if product.barcode %}
                                <div class="barcode mt-1">{{ product.barcode }}</div>
                            {% endif %}
                        </div>
                        <div class="d-flex align-items-center">
                            {% if product.image %}
                                <img src="{{ product.image.url }}" alt="{{ product.name }}" class="product-image">
                            {% else %}
                                <div class="no-image">
                                    <i class="bi bi-image" style="font-size: 2rem;"></i>
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="product-meta">
                        <div class="meta-item">
                            <i class="bi bi-tags"></i>
                            <span>{{ product.category.name }}</span>
                        </div>
                        <div class="meta-item">
                            <span class="product-type-badge">{{ product.get_product_type_display }}</span>
                        </div>
                        <div class="meta-item">
                            <i class="bi bi-rulers"></i>
                            <span>{{ product.get_main_unit_display }}</span>
                        </div>
                        <div class="meta-item">
                            <span class="status-badge {% if product.is_active %}status-active{% else %}status-inactive{% endif %}">
                                {{ product.is_active|yesno:"نشط,غير نشط" }}
                            </span>
                        </div>
                    </div>

                    <div class="price-info">
                        <div class="price-row">
                            <div class="price-item">
                                <div class="price-label">سعر التكلفة</div>
                                <div class="price-value">{{ product.cost_price }} ر.س</div>
                            </div>
                            <div class="price-item">
                                <div class="price-label">سعر البيع</div>
                                <div class="price-value">{{ product.selling_price }} ر.س</div>
                            </div>
                            <div class="price-item">
                                <div class="price-label">الحد الأدنى</div>
                                <div class="price-value">{{ product.minimum_stock }}</div>
                            </div>
                        </div>
                    </div>

                    {% if product.description %}
                    <div class="mb-3">
                        <small class="text-muted">{{ product.description|truncatechars:100 }}</small>
                    </div>
                    {% endif %}

                    <div class="product-actions">
                        <a href="#" class="btn-action btn-view">
                            <i class="bi bi-eye"></i>عرض
                        </a>
                        <a href="#" class="btn-action btn-edit">
                            <i class="bi bi-pencil"></i>تعديل
                        </a>
                        <a href="#" class="btn-action btn-delete" onclick="return confirm('هل أنت متأكد من حذف هذا الصنف؟')">
                            <i class="bi bi-trash"></i>حذف
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="bi bi-box" style="font-size: 4rem; color: #ddd;"></i>
                <h4 class="mt-3 text-muted">لا توجد أصناف</h4>
                <p class="text-muted">لم يتم العثور على أي أصناف تطابق معايير البحث</p>
                <a href="#" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>إضافة صنف جديد
                </a>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <div class="d-flex justify-content-center mt-4">
        <nav aria-label="تنقل الصفحات">
            <ul class="pagination">
                {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if product_type %}&type={{ product_type }}{% endif %}">الأولى</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if product_type %}&type={{ product_type }}{% endif %}">السابقة</a>
                    </li>
                {% endif %}

                <li class="page-item active">
                    <span class="page-link">{{ page_obj.number }} من {{ page_obj.paginator.num_pages }}</span>
                </li>

                {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if product_type %}&type={{ product_type }}{% endif %}">التالية</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if product_type %}&type={{ product_type }}{% endif %}">الأخيرة</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
    </div>
    {% endif %}
</div>
{% endblock %}
