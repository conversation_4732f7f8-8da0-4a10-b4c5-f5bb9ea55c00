# Generated by Django 5.2.4 on 2025-07-11 04:51

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Branch',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم الفرع')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='رمز الفرع')),
                ('branch_type', models.CharField(choices=[('headquarters', 'المركز الرئيسي'), ('branch', 'فرع'), ('warehouse', 'مستودع'), ('office', 'مكتب')], max_length=20, verbose_name='نوع الفرع')),
                ('address', models.TextField(verbose_name='العنوان')),
                ('city', models.Char<PERSON>ield(max_length=100, verbose_name='المدينة')),
                ('country', models.CharField(default='السعودية', max_length=100, verbose_name='الدولة')),
                ('phone', models.CharField(blank=True, max_length=20, verbose_name='رقم الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='البريد الإلكتروني')),
                ('manager_name', models.CharField(blank=True, max_length=100, verbose_name='اسم المدير')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'فرع',
                'verbose_name_plural': 'الفروع',
                'ordering': ['name'],
            },
        ),
    ]
