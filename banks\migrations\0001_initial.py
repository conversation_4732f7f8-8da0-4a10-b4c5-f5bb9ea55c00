# Generated by Django 5.2.4 on 2025-07-11 04:31

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('definitions', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Bank',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم البنك')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='رمز البنك')),
                ('swift_code', models.CharField(blank=True, max_length=11, verbose_name='رمز SWIFT')),
                ('address', models.TextField(blank=True, verbose_name='العنوان')),
                ('phone', models.CharField(blank=True, max_length=20, verbose_name='رقم الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='البريد الإلكتروني')),
                ('website', models.URLField(blank=True, verbose_name='الموقع الإلكتروني')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'بنك',
                'verbose_name_plural': 'البنوك',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='BankAccount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('account_name', models.CharField(max_length=200, verbose_name='اسم الحساب')),
                ('account_number', models.CharField(max_length=50, verbose_name='رقم الحساب')),
                ('iban', models.CharField(blank=True, max_length=34, verbose_name='رقم IBAN')),
                ('account_type', models.CharField(choices=[('current', 'حساب جاري'), ('savings', 'حساب توفير'), ('fixed', 'وديعة ثابتة'), ('investment', 'حساب استثماري')], max_length=20, verbose_name='نوع الحساب')),
                ('balance', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='الرصيد')),
                ('opening_balance', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='الرصيد الافتتاحي')),
                ('opening_date', models.DateField(verbose_name='تاريخ فتح الحساب')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('bank', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='banks.bank', verbose_name='البنك')),
                ('currency', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.currency', verbose_name='العملة')),
            ],
            options={
                'verbose_name': 'حساب بنكي',
                'verbose_name_plural': 'الحسابات البنكية',
                'ordering': ['bank', 'account_name'],
                'unique_together': {('bank', 'account_number')},
            },
        ),
        migrations.CreateModel(
            name='BankTransaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_type', models.CharField(choices=[('deposit', 'إيداع'), ('withdrawal', 'سحب'), ('transfer_in', 'تحويل وارد'), ('transfer_out', 'تحويل صادر'), ('fee', 'رسوم'), ('interest', 'فوائد')], max_length=20, verbose_name='نوع المعاملة')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='المبلغ')),
                ('description', models.CharField(max_length=500, verbose_name='الوصف')),
                ('reference_number', models.CharField(blank=True, max_length=100, verbose_name='رقم المرجع')),
                ('transaction_date', models.DateTimeField(verbose_name='تاريخ المعاملة')),
                ('balance_after', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='الرصيد بعد المعاملة')),
                ('is_reconciled', models.BooleanField(default=False, verbose_name='تم التسوية')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='banks.bankaccount', verbose_name='الحساب البنكي')),
            ],
            options={
                'verbose_name': 'معاملة بنكية',
                'verbose_name_plural': 'المعاملات البنكية',
                'ordering': ['-transaction_date'],
            },
        ),
    ]
