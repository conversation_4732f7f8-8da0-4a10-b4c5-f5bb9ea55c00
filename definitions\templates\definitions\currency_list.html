{% extends 'base.html' %}

{% block title %}إدارة العملات - أوساريك{% endblock %}

{% block content %}
    <div class="page-header d-flex justify-content-between align-items-center">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'dashboard_home' %}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'definitions:dashboard' %}">التعريفات</a></li>
                    <li class="breadcrumb-item active">العملات</li>
                </ol>
            </nav>
            <h1 class="page-title">إدارة العملات</h1>
            <p class="page-subtitle">عرض وإدارة جميع العملات وأسعار الصرف</p>
        </div>
        <a href="{% url 'definitions:currency_create' %}" class="btn btn-primary">
            <i class="bi bi-plus-circle me-2"></i>إضافة عملة جديدة
        </a>
    </div>

    <!-- Search and Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-8">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bi bi-search"></i>
                        </span>
                        <input type="text" 
                               class="form-control" 
                               name="search" 
                               value="{{ search_query }}" 
                               placeholder="البحث في أسماء العملات أو الرموز...">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="bi bi-search me-1"></i>بحث
                        </button>
                        <a href="{% url 'definitions:currency_list' %}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-clockwise me-1"></i>إعادة تعيين
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Currencies Table -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">قائمة العملات</h5>
            <span class="badge bg-primary">{{ page_obj.paginator.count }} عملة</span>
        </div>
        <div class="card-body p-0">
            {% if page_obj %}
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>اسم العملة</th>
                                <th>الرمز</th>
                                <th>الرمز المختصر</th>
                                <th>سعر الصرف</th>
                                <th>العملة الأساسية</th>
                                <th>الحالة</th>
                                <th>تاريخ الإضافة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for currency in page_obj %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="currency-icon bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3">
                                                {{ currency.symbol }}
                                            </div>
                                            <div>
                                                <h6 class="mb-0">{{ currency.name }}</h6>
                                                <small class="text-muted">#{{ currency.id }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td><span class="badge bg-secondary">{{ currency.code }}</span></td>
                                    <td><strong>{{ currency.symbol }}</strong></td>
                                    <td>{{ currency.exchange_rate }}</td>
                                    <td>
                                        {% if currency.is_base %}
                                            <span class="badge bg-success">نعم</span>
                                        {% else %}
                                            <span class="badge bg-light text-dark">لا</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if currency.is_active %}
                                            <span class="badge bg-success">نشط</span>
                                        {% else %}
                                            <span class="badge bg-danger">غير نشط</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ currency.created_at|date:"Y/m/d" }}
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{% url 'definitions:currency_edit' currency.pk %}" 
                                               class="btn btn-sm btn-outline-primary" 
                                               title="تعديل">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <a href="{% url 'definitions:currency_delete' currency.pk %}" 
                                               class="btn btn-sm btn-outline-danger" 
                                               title="حذف">
                                                <i class="bi bi-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                    <div class="card-footer">
                        <nav aria-label="صفحات العملات">
                            <ul class="pagination justify-content-center mb-0">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}">الأولى</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">السابقة</a>
                                    </li>
                                {% endif %}

                                <li class="page-item active">
                                    <span class="page-link">
                                        صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                                    </span>
                                </li>

                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">التالية</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}">الأخيرة</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-currency-exchange text-muted" style="font-size: 4rem;"></i>
                    <h4 class="mt-3 text-muted">لا توجد عملات</h4>
                    <p class="text-muted">لم يتم العثور على أي عملات مطابقة لمعايير البحث.</p>
                    <a href="{% url 'definitions:currency_create' %}" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>إضافة أول عملة
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
{% endblock %}

{% block extra_css %}
<style>
    .currency-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
        font-weight: 600;
    }
    
    .table th {
        font-weight: 600;
        border-bottom: 2px solid #dee2e6;
    }
    
    .btn-group .btn {
        border-radius: 0.375rem;
        margin-left: 2px;
    }
    
    .breadcrumb {
        background-color: transparent;
        padding: 0;
        margin-bottom: 1rem;
    }
    
    .breadcrumb-item + .breadcrumb-item::before {
        content: "←";
    }
</style>
{% endblock %}
