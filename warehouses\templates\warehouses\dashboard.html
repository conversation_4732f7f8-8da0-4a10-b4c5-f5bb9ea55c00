{% extends 'base.html' %}

{% block title %}لوحة تحكم المخازن - أوساريك{% endblock %}

{% block content %}
    <!-- Enhanced Header with Gradient Background -->
    <div class="warehouse-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <div class="header-content">
                        <div class="header-icon">
                            <i class="bi bi-boxes"></i>
                        </div>
                        <div class="header-text">
                            <h1 class="header-title">لوحة تحكم المخازن</h1>
                            <p class="header-subtitle">نظام إدارة شامل ومتطور للمخازن والمخزون وحركة البضائع</p>
                            <div class="header-breadcrumb">
                                <span class="breadcrumb-item">
                                    <i class="bi bi-house-door me-1"></i>الرئيسية
                                </span>
                                <span class="breadcrumb-separator">→</span>
                                <span class="breadcrumb-item active">المخازن</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 text-end">
                    <div class="header-actions">
                        <button class="btn btn-glass me-2" onclick="refreshDashboard()" data-bs-toggle="tooltip" title="تحديث البيانات">
                            <i class="bi bi-arrow-clockwise"></i>
                        </button>
                        <div class="dropdown d-inline-block">
                            <button class="btn btn-primary-gradient dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="bi bi-lightning-charge me-2"></i>إجراءات سريعة
                            </button>
                            <ul class="dropdown-menu dropdown-menu-modern">
                                <li class="dropdown-header">
                                    <i class="bi bi-gear me-2"></i>العمليات الأساسية
                                </li>
                                <li><a class="dropdown-item" href="{% url 'warehouses:warehouse_create' %}">
                                    <div class="dropdown-icon bg-primary">
                                        <i class="bi bi-building"></i>
                                    </div>
                                    <div class="dropdown-content">
                                        <span class="dropdown-title">إضافة مخزن جديد</span>
                                        <small class="dropdown-desc">إنشاء مخزن أو مستودع جديد</small>
                                    </div>
                                </a></li>
                                <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#addStockModal">
                                    <div class="dropdown-icon bg-success">
                                        <i class="bi bi-plus-square"></i>
                                    </div>
                                    <div class="dropdown-content">
                                        <span class="dropdown-title">إذن إضافة مخزون</span>
                                        <small class="dropdown-desc">زيادة كمية المنتجات</small>
                                    </div>
                                </a></li>
                                <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#issueStockModal">
                                    <div class="dropdown-icon bg-danger">
                                        <i class="bi bi-dash-square"></i>
                                    </div>
                                    <div class="dropdown-content">
                                        <span class="dropdown-title">إذن صرف مخزون</span>
                                        <small class="dropdown-desc">تقليل كمية المنتجات</small>
                                    </div>
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li class="dropdown-header">
                                    <i class="bi bi-arrow-left-right me-2"></i>العمليات المتقدمة
                                </li>
                                <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#transferStockModal">
                                    <div class="dropdown-icon bg-warning">
                                        <i class="bi bi-truck"></i>
                                    </div>
                                    <div class="dropdown-content">
                                        <span class="dropdown-title">تحويل بين المخازن</span>
                                        <small class="dropdown-desc">نقل البضائع بين المواقع</small>
                                    </div>
                                </a></li>
                                <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#adjustmentModal">
                                    <div class="dropdown-icon bg-secondary">
                                        <i class="bi bi-gear"></i>
                                    </div>
                                    <div class="dropdown-content">
                                        <span class="dropdown-title">تسوية المخزون</span>
                                        <small class="dropdown-desc">تصحيح الكميات والأخطاء</small>
                                    </div>
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Statistics Cards -->
    <div class="stats-section">
        <div class="container-fluid">
            <div class="row g-4 mb-5">
                <!-- Total Warehouses Card -->
                <div class="col-xl-3 col-lg-6 col-md-6">
                    <div class="stats-card-modern">
                        <div class="stats-card-header">
                            <div class="stats-icon-modern bg-gradient-primary">
                                <i class="bi bi-building"></i>
                            </div>
                            <div class="stats-actions">
                                <div class="dropdown">
                                    <button class="btn btn-stats-action" data-bs-toggle="dropdown">
                                        <i class="bi bi-three-dots"></i>
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-end">
                                        <li><a class="dropdown-item" href="{% url 'warehouses:warehouse_list' %}">
                                            <i class="bi bi-eye me-2"></i>عرض الكل
                                        </a></li>
                                        <li><a class="dropdown-item" href="{% url 'warehouses:warehouse_create' %}">
                                            <i class="bi bi-plus me-2"></i>إضافة جديد
                                        </a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="stats-card-body">
                            <div class="stats-number">{{ total_warehouses }}</div>
                            <div class="stats-label">إجمالي المخازن</div>
                            <div class="stats-progress">
                                <div class="progress">
                                    <div class="progress-bar bg-primary" style="width: {{ warehouse_active_percentage }}%"></div>
                                </div>
                                <small class="stats-progress-text">
                                    <span class="text-success">{{ active_warehouses }} نشط</span>
                                    <span class="text-muted">من {{ total_warehouses }}</span>
                                </small>
                            </div>
                        </div>
                        <div class="stats-card-footer">
                            <a href="{% url 'warehouses:warehouse_list' %}" class="stats-link">
                                عرض التفاصيل <i class="bi bi-arrow-left ms-1"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Total Products Card -->
                <div class="col-xl-3 col-lg-6 col-md-6">
                    <div class="stats-card-modern">
                        <div class="stats-card-header">
                            <div class="stats-icon-modern bg-gradient-success">
                                <i class="bi bi-box"></i>
                            </div>
                            <div class="stats-actions">
                                <div class="dropdown">
                                    <button class="btn btn-stats-action" data-bs-toggle="dropdown">
                                        <i class="bi bi-three-dots"></i>
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-end">
                                        <li><a class="dropdown-item" href="{% url 'warehouses:stock_list' %}">
                                            <i class="bi bi-boxes me-2"></i>عرض المخزون
                                        </a></li>
                                        <li><a class="dropdown-item" href="{% url 'products:product_list' %}">
                                            <i class="bi bi-grid me-2"></i>إدارة المنتجات
                                        </a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="stats-card-body">
                            <div class="stats-number">{{ total_products }}</div>
                            <div class="stats-label">أصناف المنتجات</div>
                            <div class="stats-info">
                                <span class="badge badge-soft-success">
                                    <i class="bi bi-check-circle me-1"></i>في المخزون
                                </span>
                            </div>
                        </div>
                        <div class="stats-card-footer">
                            <a href="{% url 'warehouses:stock_list' %}" class="stats-link">
                                عرض المخزون <i class="bi bi-arrow-left ms-1"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Total Stock Value Card -->
                <div class="col-xl-3 col-lg-6 col-md-6">
                    <div class="stats-card-modern">
                        <div class="stats-card-header">
                            <div class="stats-icon-modern bg-gradient-info">
                                <i class="bi bi-boxes"></i>
                            </div>
                            <div class="stats-actions">
                                <div class="dropdown">
                                    <button class="btn btn-stats-action" data-bs-toggle="dropdown">
                                        <i class="bi bi-three-dots"></i>
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-end">
                                        <li><a class="dropdown-item" href="{% url 'warehouses:stock_list' %}">
                                            <i class="bi bi-list-ul me-2"></i>تفاصيل المخزون
                                        </a></li>
                                        <li><a class="dropdown-item" href="#">
                                            <i class="bi bi-file-earmark-bar-graph me-2"></i>تقرير الكميات
                                        </a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="stats-card-body">
                            <div class="stats-number">{{ total_stock_value|floatformat:0 }}</div>
                            <div class="stats-label">إجمالي الكميات</div>
                            <div class="stats-info">
                                <span class="badge badge-soft-info">
                                    <i class="bi bi-calculator me-1"></i>وحدة
                                </span>
                            </div>
                        </div>
                        <div class="stats-card-footer">
                            <a href="{% url 'warehouses:stock_list' %}" class="stats-link">
                                عرض التفاصيل <i class="bi bi-arrow-left ms-1"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Low Stock Alert Card -->
                <div class="col-xl-3 col-lg-6 col-md-6">
                    <div class="stats-card-modern stats-card-alert">
                        <div class="stats-card-header">
                            <div class="stats-icon-modern bg-gradient-warning">
                                <i class="bi bi-exclamation-triangle"></i>
                            </div>
                            <div class="stats-actions">
                                <div class="dropdown">
                                    <button class="btn btn-stats-action" data-bs-toggle="dropdown">
                                        <i class="bi bi-three-dots"></i>
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-end">
                                        <li><a class="dropdown-item" href="{% url 'warehouses:stock_list' %}?low_stock=1">
                                            <i class="bi bi-exclamation-triangle me-2"></i>عرض المنخفض
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#addStockModal">
                                            <i class="bi bi-plus-square me-2"></i>إذن إضافة
                                        </a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="stats-card-body">
                            <div class="stats-number text-warning">{{ low_stock_items }}</div>
                            <div class="stats-label">تنبيهات المخزون</div>
                            <div class="stats-info">
                                {% if low_stock_items > 0 %}
                                    <span class="badge badge-soft-danger pulse">
                                        <i class="bi bi-arrow-down me-1"></i>يحتاج انتباه
                                    </span>
                                {% else %}
                                    <span class="badge badge-soft-success">
                                        <i class="bi bi-check-circle me-1"></i>مستوى طبيعي
                                    </span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="stats-card-footer">
                            <a href="{% url 'warehouses:stock_list' %}?low_stock=1" class="stats-link">
                                عرض التنبيهات <i class="bi bi-arrow-left ms-1"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Mini Statistics -->
    <div class="mini-stats-section">
        <div class="container-fluid">
            <div class="row g-3 mb-5">
                <!-- Today's Movements -->
                <div class="col-xl-2 col-lg-4 col-md-6">
                    <div class="mini-stats-card">
                        <div class="mini-stats-icon bg-gradient-primary">
                            <i class="bi bi-arrow-up-down"></i>
                        </div>
                        <div class="mini-stats-content">
                            <div class="mini-stats-number">{{ today_movements }}</div>
                            <div class="mini-stats-label">حركات اليوم</div>
                        </div>
                        <div class="mini-stats-trend">
                            <i class="bi bi-graph-up text-success"></i>
                        </div>
                    </div>
                </div>

                <!-- Pending Transfers -->
                <div class="col-xl-2 col-lg-4 col-md-6">
                    <div class="mini-stats-card">
                        <div class="mini-stats-icon bg-gradient-warning">
                            <i class="bi bi-truck"></i>
                        </div>
                        <div class="mini-stats-content">
                            <div class="mini-stats-number">{{ pending_transfers }}</div>
                            <div class="mini-stats-label">تحويلات معلقة</div>
                        </div>
                        <div class="mini-stats-trend">
                            {% if pending_transfers > 0 %}
                                <i class="bi bi-clock text-warning pulse"></i>
                            {% else %}
                                <i class="bi bi-check-circle text-success"></i>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Out of Stock -->
                <div class="col-xl-2 col-lg-4 col-md-6">
                    <div class="mini-stats-card">
                        <div class="mini-stats-icon bg-gradient-danger">
                            <i class="bi bi-x-circle"></i>
                        </div>
                        <div class="mini-stats-content">
                            <div class="mini-stats-number">{{ out_of_stock }}</div>
                            <div class="mini-stats-label">منتجات منتهية</div>
                        </div>
                        <div class="mini-stats-trend">
                            {% if out_of_stock > 0 %}
                                <i class="bi bi-exclamation-triangle text-danger"></i>
                            {% else %}
                                <i class="bi bi-check-circle text-success"></i>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Total Locations -->
                <div class="col-xl-2 col-lg-4 col-md-6">
                    <div class="mini-stats-card">
                        <div class="mini-stats-icon bg-gradient-info">
                            <i class="bi bi-geo-alt"></i>
                        </div>
                        <div class="mini-stats-content">
                            <div class="mini-stats-number">{{ total_locations }}</div>
                            <div class="mini-stats-label">مواقع التخزين</div>
                        </div>
                        <div class="mini-stats-trend">
                            <i class="bi bi-diagram-3 text-info"></i>
                        </div>
                    </div>
                </div>

                <!-- Reserved Stock -->
                <div class="col-xl-2 col-lg-4 col-md-6">
                    <div class="mini-stats-card">
                        <div class="mini-stats-icon bg-gradient-secondary">
                            <i class="bi bi-lock"></i>
                        </div>
                        <div class="mini-stats-content">
                            <div class="mini-stats-number">{{ reserved_stock|floatformat:0 }}</div>
                            <div class="mini-stats-label">مخزون محجوز</div>
                        </div>
                        <div class="mini-stats-trend">
                            <i class="bi bi-shield-lock text-secondary"></i>
                        </div>
                    </div>
                </div>

                <!-- Available Stock -->
                <div class="col-xl-2 col-lg-4 col-md-6">
                    <div class="mini-stats-card">
                        <div class="mini-stats-icon bg-gradient-success">
                            <i class="bi bi-check-circle"></i>
                        </div>
                        <div class="mini-stats-content">
                            <div class="mini-stats-number">{{ available_stock|floatformat:0 }}</div>
                            <div class="mini-stats-label">مخزون متاح</div>
                        </div>
                        <div class="mini-stats-trend">
                            <i class="bi bi-check2-all text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Quick Actions Section -->
    <div class="actions-section">
        <div class="container-fluid">
            <div class="section-header mb-4">
                <h3 class="section-title">
                    <i class="bi bi-lightning-charge me-2"></i>الإجراءات السريعة
                </h3>
                <p class="section-subtitle">العمليات الأساسية لإدارة المخازن والمخزون</p>
            </div>

            <!-- Main Actions Grid -->
            <div class="row g-4 mb-5">
                <!-- Warehouse Management -->
                <div class="col-xl-3 col-lg-6 col-md-6">
                    <div class="action-card-enhanced">
                        <div class="action-card-header">
                            <div class="action-icon-large bg-gradient-primary">
                                <i class="bi bi-building"></i>
                            </div>
                            <div class="action-badge">
                                <span class="badge bg-primary">{{ total_warehouses }}</span>
                            </div>
                        </div>
                        <div class="action-card-body">
                            <h5 class="action-title">إدارة المخازن</h5>
                            <p class="action-description">إضافة وإدارة المخازن والمستودعات والمناطق</p>
                            <div class="action-buttons">
                                <a href="{% url 'warehouses:warehouse_list' %}" class="btn btn-primary btn-action">
                                    <i class="bi bi-list me-2"></i>عرض المخازن
                                </a>
                                <a href="{% url 'warehouses:warehouse_create' %}" class="btn btn-outline-primary btn-action">
                                    <i class="bi bi-plus me-2"></i>إضافة مخزن
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Stock Management -->
                <div class="col-xl-3 col-lg-6 col-md-6">
                    <div class="action-card-enhanced">
                        <div class="action-card-header">
                            <div class="action-icon-large bg-gradient-success">
                                <i class="bi bi-boxes"></i>
                            </div>
                            <div class="action-badge">
                                <span class="badge bg-success">{{ total_products }}</span>
                            </div>
                        </div>
                        <div class="action-card-body">
                            <h5 class="action-title">إدارة المخزون</h5>
                            <p class="action-description">متابعة وإدارة مخزون المنتجات والكميات</p>
                            <div class="action-buttons">
                                <a href="{% url 'warehouses:stock_list' %}" class="btn btn-success btn-action">
                                    <i class="bi bi-eye me-2"></i>عرض المخزون
                                </a>
                                <button class="btn btn-outline-success btn-action" data-bs-toggle="modal" data-bs-target="#addStockModal">
                                    <i class="bi bi-plus-square me-2"></i>إذن إضافة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Stock Movements -->
                <div class="col-xl-3 col-lg-6 col-md-6">
                    <div class="action-card-enhanced">
                        <div class="action-card-header">
                            <div class="action-icon-large bg-gradient-info">
                                <i class="bi bi-arrow-up-down"></i>
                            </div>
                            <div class="action-badge">
                                <span class="badge bg-info">{{ today_movements }}</span>
                            </div>
                        </div>
                        <div class="action-card-body">
                            <h5 class="action-title">حركة المخزون</h5>
                            <p class="action-description">متابعة حركات الوارد والصادر والتسويات</p>
                            <div class="action-buttons">
                                <a href="{% url 'warehouses:stock_movements' %}" class="btn btn-info btn-action">
                                    <i class="bi bi-list me-2"></i>عرض الحركات
                                </a>
                                <button class="btn btn-outline-info btn-action" data-bs-toggle="modal" data-bs-target="#issueStockModal">
                                    <i class="bi bi-dash-square me-2"></i>إذن صرف
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Stock Transfers -->
                <div class="col-xl-3 col-lg-6 col-md-6">
                    <div class="action-card-enhanced">
                        <div class="action-card-header">
                            <div class="action-icon-large bg-gradient-warning">
                                <i class="bi bi-truck"></i>
                            </div>
                            <div class="action-badge">
                                <span class="badge bg-warning">{{ pending_transfers }}</span>
                            </div>
                        </div>
                        <div class="action-card-body">
                            <h5 class="action-title">تحويل المخزون</h5>
                            <p class="action-description">تحويل البضائع بين المخازن المختلفة</p>
                            <div class="action-buttons">
                                <a href="{% url 'warehouses:stock_transfers' %}" class="btn btn-warning btn-action">
                                    <i class="bi bi-list me-2"></i>عرض التحويلات
                                </a>
                                <button class="btn btn-outline-warning btn-action" data-bs-toggle="modal" data-bs-target="#transferStockModal">
                                    <i class="bi bi-arrow-left-right me-2"></i>تحويل جديد
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Secondary Actions Row -->
            <div class="row g-4 mb-5">
                <!-- Stock Adjustment -->
                <div class="col-xl-4 col-lg-6">
                    <div class="secondary-action-card">
                        <div class="secondary-action-content">
                            <div class="secondary-action-icon bg-gradient-secondary">
                                <i class="bi bi-gear"></i>
                            </div>
                            <div class="secondary-action-text">
                                <h6 class="secondary-action-title">تسوية المخزون</h6>
                                <p class="secondary-action-desc">تصحيح الكميات والأخطاء</p>
                            </div>
                            <button class="btn btn-outline-secondary btn-sm" data-bs-toggle="modal" data-bs-target="#adjustmentModal">
                                <i class="bi bi-gear me-1"></i>تسوية
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Reports -->
                <div class="col-xl-4 col-lg-6">
                    <div class="secondary-action-card">
                        <div class="secondary-action-content">
                            <div class="secondary-action-icon bg-gradient-dark">
                                <i class="bi bi-file-earmark-bar-graph"></i>
                            </div>
                            <div class="secondary-action-text">
                                <h6 class="secondary-action-title">التقارير</h6>
                                <p class="secondary-action-desc">تقارير المخزون والحركات</p>
                            </div>
                            <div class="dropdown">
                                <button class="btn btn-outline-dark btn-sm dropdown-toggle" data-bs-toggle="dropdown">
                                    <i class="bi bi-file-text me-1"></i>تقارير
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li><a class="dropdown-item" href="#">
                                        <i class="bi bi-file-earmark-text me-2"></i>تقرير المخزون
                                    </a></li>
                                    <li><a class="dropdown-item" href="#">
                                        <i class="bi bi-arrow-left-right me-2"></i>تقرير الحركات
                                    </a></li>
                                    <li><a class="dropdown-item" href="#">
                                        <i class="bi bi-truck me-2"></i>تقرير التحويلات
                                    </a></li>
                                    <li><a class="dropdown-item" href="#">
                                        <i class="bi bi-exclamation-triangle me-2"></i>تقرير المنخفض
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Settings -->
                <div class="col-xl-4 col-lg-6">
                    <div class="secondary-action-card">
                        <div class="secondary-action-content">
                            <div class="secondary-action-icon bg-gradient-purple">
                                <i class="bi bi-sliders"></i>
                            </div>
                            <div class="secondary-action-text">
                                <h6 class="secondary-action-title">إعدادات المخازن</h6>
                                <p class="secondary-action-desc">إعدادات النظام والتنبيهات</p>
                            </div>
                            <button class="btn btn-outline-purple btn-sm">
                                <i class="bi bi-sliders me-1"></i>إعدادات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Analytics and Reports Section -->
    <div class="row g-4 mb-4">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-pie-chart me-2"></i>توزيع المخازن حسب النوع
                    </h5>
                </div>
                <div class="card-body">
                    {% if warehouse_stats %}
                        <div class="row g-3">
                            {% for stat in warehouse_stats %}
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center p-3 border rounded">
                                        <div class="warehouse-type-icon me-3">
                                            {% if stat.warehouse_type == 'main' %}
                                                <i class="bi bi-building text-primary" style="font-size: 2rem;"></i>
                                            {% elif stat.warehouse_type == 'branch' %}
                                                <i class="bi bi-geo-alt text-success" style="font-size: 2rem;"></i>
                                            {% elif stat.warehouse_type == 'temporary' %}
                                                <i class="bi bi-clock text-info" style="font-size: 2rem;"></i>
                                            {% elif stat.warehouse_type == 'damaged' %}
                                                <i class="bi bi-exclamation-triangle text-danger" style="font-size: 2rem;"></i>
                                            {% elif stat.warehouse_type == 'returns' %}
                                                <i class="bi bi-arrow-return-left text-warning" style="font-size: 2rem;"></i>
                                            {% endif %}
                                        </div>
                                        <div>
                                            <h6 class="mb-1">
                                                {% if stat.warehouse_type == 'main' %}مخزن رئيسي
                                                {% elif stat.warehouse_type == 'branch' %}مخزن فرع
                                                {% elif stat.warehouse_type == 'temporary' %}مخزن مؤقت
                                                {% elif stat.warehouse_type == 'damaged' %}مخزن تالف
                                                {% elif stat.warehouse_type == 'returns' %}مخزن مرتجعات
                                                {% endif %}
                                            </h6>
                                            <span class="badge bg-primary">{{ stat.count }} مخزن</span>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="bi bi-building text-muted" style="font-size: 3rem;"></i>
                            <h5 class="mt-3 text-muted">لا توجد مخازن</h5>
                            <p class="text-muted">لم يتم إضافة أي مخازن بعد.</p>
                            <a href="{% url 'warehouses:warehouse_create' %}" class="btn btn-primary">
                                <i class="bi bi-plus me-2"></i>إضافة أول مخزن
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-exclamation-triangle me-2"></i>تنبيهات المخزون
                    </h5>
                </div>
                <div class="card-body">
                    {% if reorder_items %}
                        <div class="list-group list-group-flush">
                            {% for item in reorder_items %}
                                <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <div>
                                        <h6 class="mb-1">{{ item.product.name }}</h6>
                                        <small class="text-muted">{{ item.warehouse.name }}</small>
                                    </div>
                                    <span class="badge bg-warning">{{ item.quantity }}</span>
                                </div>
                            {% endfor %}
                        </div>
                        <div class="mt-3">
                            <a href="{% url 'warehouses:stock_list' %}?low_stock=1" class="btn btn-outline-warning btn-sm w-100">
                                عرض جميع المنتجات المنخفضة
                            </a>
                        </div>
                    {% else %}
                        <div class="text-center py-3">
                            <i class="bi bi-check-circle text-success" style="font-size: 2rem;"></i>
                            <p class="text-muted mt-2 mb-0">جميع المنتجات في المستوى الطبيعي</p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Recent Movements -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-clock-history me-2"></i>آخر حركات المخزون
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_movements %}
                        <div class="list-group list-group-flush">
                            {% for movement in recent_movements %}
                                <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <div>
                                        <h6 class="mb-1">{{ movement.product.name }}</h6>
                                        <small class="text-muted">
                                            {{ movement.get_movement_type_display }} - {{ movement.warehouse.name }}
                                        </small>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge {% if movement.movement_type == 'in' %}bg-success{% else %}bg-danger{% endif %}">
                                            {% if movement.movement_type == 'in' %}+{% else %}-{% endif %}{{ movement.quantity }}
                                        </span>
                                        <br>
                                        <small class="text-muted">{{ movement.created_at|date:"m/d H:i" }}</small>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                        <div class="mt-3">
                            <a href="{% url 'warehouses:stock_movements' %}" class="btn btn-outline-primary btn-sm w-100">
                                عرض جميع الحركات
                            </a>
                        </div>
                    {% else %}
                        <div class="text-center py-3">
                            <i class="bi bi-arrow-left-right text-muted" style="font-size: 2rem;"></i>
                            <p class="text-muted mt-2 mb-0">لا توجد حركات حديثة</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Add Stock Modal (إذن إضافة) -->
    <div class="modal fade" id="addStockModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title">
                        <i class="bi bi-plus-square me-2"></i>إذن إضافة مخزون
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addStockForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">المخزن</label>
                                <select class="form-select" name="warehouse" required>
                                    <option value="">اختر المخزن</option>
                                    {% for warehouse in warehouses %}
                                        <option value="{{ warehouse.id }}">{{ warehouse.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">المنتج</label>
                                <select class="form-select" name="product" required>
                                    <option value="">اختر المنتج</option>
                                    <!-- سيتم تحميل المنتجات بـ AJAX -->
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label class="form-label">الكمية</label>
                                <input type="number" class="form-control" name="quantity" step="0.01" min="0" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">تكلفة الوحدة</label>
                                <input type="number" class="form-control" name="unit_cost" step="0.01" min="0">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">رقم المرجع</label>
                                <input type="text" class="form-control" name="reference_number" placeholder="رقم الفاتورة أو المرجع">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control" name="notes" rows="3" placeholder="ملاحظات إضافية..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-success" onclick="submitAddStock(this)">
                        <i class="bi bi-check-lg me-2"></i>تأكيد الإضافة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Issue Stock Modal (إذن صرف) -->
    <div class="modal fade" id="issueStockModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">
                        <i class="bi bi-dash-square me-2"></i>إذن صرف مخزون
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="issueStockForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">المخزن</label>
                                <select class="form-select" name="warehouse" required>
                                    <option value="">اختر المخزن</option>
                                    {% for warehouse in warehouses %}
                                        <option value="{{ warehouse.id }}">{{ warehouse.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">المنتج</label>
                                <select class="form-select" name="product" required>
                                    <option value="">اختر المنتج</option>
                                    <!-- سيتم تحميل المنتجات بـ AJAX -->
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label class="form-label">الكمية المطلوبة</label>
                                <input type="number" class="form-control" name="quantity" step="0.01" min="0" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">الكمية المتاحة</label>
                                <input type="number" class="form-control" name="available_quantity" readonly>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">رقم المرجع</label>
                                <input type="text" class="form-control" name="reference_number" placeholder="رقم الطلب أو المرجع">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">سبب الصرف</label>
                            <select class="form-select" name="reference_type" required>
                                <option value="">اختر السبب</option>
                                <option value="sale">بيع</option>
                                <option value="production">إنتاج</option>
                                <option value="damage">تلف</option>
                                <option value="return">مرتجع</option>
                                <option value="manual">يدوي</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control" name="notes" rows="3" placeholder="ملاحظات إضافية..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-danger" onclick="submitIssueStock(this)">
                        <i class="bi bi-check-lg me-2"></i>تأكيد الصرف
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Transfer Stock Modal (تحويل بين المخازن) -->
    <div class="modal fade" id="transferStockModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title">
                        <i class="bi bi-arrow-left-right me-2"></i>تحويل مخزون بين المخازن
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="transferStockForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">من المخزن</label>
                                <select class="form-select" name="from_warehouse" required>
                                    <option value="">اختر المخزن المرسل</option>
                                    {% for warehouse in warehouses %}
                                        <option value="{{ warehouse.id }}">{{ warehouse.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">إلى المخزن</label>
                                <select class="form-select" name="to_warehouse" required>
                                    <option value="">اختر المخزن المستقبل</option>
                                    {% for warehouse in warehouses %}
                                        <option value="{{ warehouse.id }}">{{ warehouse.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label class="form-label">رقم التحويل</label>
                                <input type="text" class="form-control" name="transfer_number" value="TR-{{ today|date:'Ymd' }}-001" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">تاريخ التحويل</label>
                                <input type="datetime-local" class="form-control" name="transfer_date" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">تاريخ الوصول المتوقع</label>
                                <input type="datetime-local" class="form-control" name="expected_arrival">
                            </div>
                        </div>

                        <!-- Transfer Items -->
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <label class="form-label mb-0">عناصر التحويل</label>
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="addTransferItem()">
                                    <i class="bi bi-plus me-1"></i>إضافة منتج
                                </button>
                            </div>
                            <div id="transferItems">
                                <div class="transfer-item border rounded p-3 mb-2">
                                    <div class="row">
                                        <div class="col-md-6 mb-2">
                                            <select class="form-select" name="products[]" required>
                                                <option value="">اختر المنتج</option>
                                                <!-- سيتم تحميل المنتجات بـ AJAX -->
                                            </select>
                                        </div>
                                        <div class="col-md-3 mb-2">
                                            <input type="number" class="form-control" name="quantities[]" placeholder="الكمية" step="0.01" min="0" required>
                                        </div>
                                        <div class="col-md-2 mb-2">
                                            <span class="form-control-plaintext text-muted">متاح: <span class="available-qty">0</span></span>
                                        </div>
                                        <div class="col-md-1 mb-2">
                                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeTransferItem(this)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control" name="notes" rows="3" placeholder="ملاحظات التحويل..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-warning" onclick="submitTransferStock(this)">
                        <i class="bi bi-check-lg me-2"></i>تأكيد التحويل
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Stock Adjustment Modal (تسوية المخزون) -->
    <div class="modal fade" id="adjustmentModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-secondary text-white">
                    <h5 class="modal-title">
                        <i class="bi bi-gear me-2"></i>تسوية المخزون
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="adjustmentForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">المخزن</label>
                                <select class="form-select" name="warehouse" required>
                                    <option value="">اختر المخزن</option>
                                    {% for warehouse in warehouses %}
                                        <option value="{{ warehouse.id }}">{{ warehouse.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">نوع التسوية</label>
                                <select class="form-select" name="adjustment_type" required>
                                    <option value="">اختر النوع</option>
                                    <option value="increase">زيادة</option>
                                    <option value="decrease">نقص</option>
                                    <option value="correction">تصحيح</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">رقم التسوية</label>
                                <input type="text" class="form-control" name="adjustment_number" value="ADJ-{{ today|date:'Ymd' }}-001" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">تاريخ التسوية</label>
                                <input type="datetime-local" class="form-control" name="adjustment_date" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">سبب التسوية</label>
                            <textarea class="form-control" name="reason" rows="3" placeholder="اذكر سبب التسوية..." required></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">معتمد من</label>
                            <input type="text" class="form-control" name="approved_by" placeholder="اسم المعتمد">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-secondary" onclick="submitAdjustment(this)">
                        <i class="bi bi-check-lg me-2"></i>تأكيد التسوية
                    </button>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block extra_css %}
<style>
    /* Modern Warehouse Dashboard Styles */
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        --info-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --danger-gradient: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
        --secondary-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        --glass-bg: rgba(255, 255, 255, 0.25);
        --glass-border: rgba(255, 255, 255, 0.18);
    }

    /* Enhanced Header */
    .warehouse-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 3rem 0;
        margin-bottom: 3rem;
        position: relative;
        overflow: hidden;
    }

    .warehouse-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
    }

    .header-content {
        display: flex;
        align-items: center;
        position: relative;
        z-index: 2;
    }

    .header-icon {
        width: 80px;
        height: 80px;
        background: var(--glass-bg);
        backdrop-filter: blur(10px);
        border: 1px solid var(--glass-border);
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 2rem;
        font-size: 2rem;
        color: white;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    }

    .header-text .header-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .header-text .header-subtitle {
        color: rgba(255,255,255,0.9);
        font-size: 1.1rem;
        margin-bottom: 1rem;
    }

    .header-breadcrumb {
        display: flex;
        align-items: center;
        color: rgba(255,255,255,0.8);
        font-size: 0.9rem;
    }

    .breadcrumb-separator {
        margin: 0 0.5rem;
        opacity: 0.6;
    }

    .breadcrumb-item.active {
        color: white;
        font-weight: 500;
    }

    .header-actions {
        position: relative;
        z-index: 2;
    }

    .btn-glass {
        background: var(--glass-bg);
        backdrop-filter: blur(10px);
        border: 1px solid var(--glass-border);
        color: white;
        border-radius: 12px;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
    }

    .btn-glass:hover {
        background: rgba(255,255,255,0.35);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .btn-primary-gradient {
        background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%);
        border: none;
        color: white;
        border-radius: 12px;
        padding: 0.75rem 1.5rem;
        font-weight: 500;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    }

    .btn-primary-gradient:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.25);
        color: white;
    }

    /* Enhanced Dropdown Menus */
    .dropdown-menu {
        border: none;
        border-radius: 12px;
        box-shadow: 0 10px 40px rgba(0,0,0,0.15);
        padding: 0.5rem 0;
        background: white;
        z-index: 1050;
        min-width: 200px;
    }

    .dropdown-menu .dropdown-item {
        display: flex;
        align-items: center;
        padding: 0.75rem 1rem;
        border: none;
        transition: all 0.3s ease;
        color: #495057;
        font-size: 0.9rem;
    }

    .dropdown-menu .dropdown-item:hover {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        transform: translateX(3px);
    }

    .dropdown-menu .dropdown-item i {
        width: 20px;
        text-align: center;
    }

    .dropdown-menu-modern {
        border: none;
        border-radius: 15px;
        box-shadow: 0 20px 60px rgba(0,0,0,0.15);
        padding: 1rem 0;
        backdrop-filter: blur(10px);
        background: rgba(255,255,255,0.95);
        z-index: 1050;
    }

    .dropdown-menu-modern .dropdown-item {
        display: flex;
        align-items: center;
        padding: 0.75rem 1.5rem;
        border: none;
        transition: all 0.3s ease;
    }

    .dropdown-menu-modern .dropdown-item:hover {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        transform: translateX(5px);
    }

    .dropdown-icon {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        color: white;
        font-size: 1.1rem;
    }

    .dropdown-content {
        flex: 1;
    }

    .dropdown-title {
        font-weight: 600;
        display: block;
        margin-bottom: 0.25rem;
    }

    .dropdown-desc {
        color: #6c757d;
        font-size: 0.85rem;
    }

    .dropdown-header {
        font-weight: 600;
        color: #495057;
        padding: 0.5rem 1.5rem;
        font-size: 0.85rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    /* Ensure dropdowns appear above other elements */
    .dropdown {
        position: relative;
        z-index: 1000;
    }

    .dropdown-toggle::after {
        margin-left: 0.5rem;
    }

    /* Fix for dropdown positioning */
    .dropdown-menu-end {
        right: 0;
        left: auto;
    }
    /* Modern Stats Cards */
    .stats-section {
        margin-bottom: 3rem;
    }

    .stats-card-modern {
        background: white;
        border-radius: 20px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
        overflow: hidden;
        position: relative;
        border: 1px solid rgba(0,0,0,0.05);
    }

    .stats-card-modern:hover {
        transform: translateY(-8px);
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }

    .stats-card-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--primary-gradient);
    }

    .stats-card-alert::before {
        background: var(--warning-gradient);
    }

    .stats-card-header {
        display: flex;
        justify-content: between;
        align-items: center;
        padding: 1.5rem 1.5rem 0;
    }

    .stats-icon-modern {
        width: 60px;
        height: 60px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        position: relative;
        overflow: hidden;
    }

    .bg-gradient-primary { background: var(--primary-gradient); }
    .bg-gradient-success { background: var(--success-gradient); }
    .bg-gradient-info { background: var(--info-gradient); }
    .bg-gradient-warning { background: var(--warning-gradient); }
    .bg-gradient-danger { background: var(--danger-gradient); }
    .bg-gradient-secondary { background: var(--secondary-gradient); }

    .stats-icon-modern::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
        animation: shimmer 3s infinite;
    }

    @keyframes shimmer {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .stats-actions {
        margin-left: auto;
    }

    .btn-stats-action {
        background: transparent;
        border: 1px solid rgba(0,0,0,0.1);
        border-radius: 8px;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #6c757d;
        transition: all 0.3s ease;
    }

    .btn-stats-action:hover {
        background: #f8f9fa;
        color: #495057;
        transform: scale(1.1);
    }

    .stats-card-body {
        padding: 1rem 1.5rem;
    }

    .stats-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: #2d3748;
        margin-bottom: 0.5rem;
        line-height: 1;
    }

    .stats-label {
        font-size: 0.95rem;
        color: #718096;
        font-weight: 500;
        margin-bottom: 1rem;
    }

    .stats-progress {
        margin-top: 1rem;
    }

    .stats-progress .progress {
        height: 6px;
        border-radius: 3px;
        background: #e2e8f0;
        margin-bottom: 0.5rem;
    }

    .stats-progress-text {
        display: flex;
        justify-content: space-between;
        font-size: 0.8rem;
    }

    .stats-info {
        margin-top: 0.5rem;
    }

    .badge-soft-primary { background: rgba(102, 126, 234, 0.1); color: #667eea; }
    .badge-soft-success { background: rgba(17, 153, 142, 0.1); color: #11998e; }
    .badge-soft-info { background: rgba(102, 126, 234, 0.1); color: #667eea; }
    .badge-soft-warning { background: rgba(240, 147, 251, 0.1); color: #f093fb; }
    .badge-soft-danger { background: rgba(252, 70, 107, 0.1); color: #fc466b; }

    .stats-card-footer {
        padding: 0 1.5rem 1.5rem;
    }

    .stats-link {
        color: #667eea;
        text-decoration: none;
        font-size: 0.9rem;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .stats-link:hover {
        color: #764ba2;
        text-decoration: none;
        transform: translateX(-3px);
    }

    /* Mini Stats Cards */
    .mini-stats-section {
        margin-bottom: 3rem;
    }

    .mini-stats-card {
        background: white;
        border-radius: 16px;
        padding: 1.5rem;
        box-shadow: 0 2px 12px rgba(0,0,0,0.06);
        transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        border: 1px solid rgba(0,0,0,0.05);
        display: flex;
        align-items: center;
        position: relative;
        overflow: hidden;
    }

    .mini-stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: var(--primary-gradient);
        transition: width 0.3s ease;
    }

    .mini-stats-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.12);
    }

    .mini-stats-card:hover::before {
        width: 100%;
        opacity: 0.05;
    }

    .mini-stats-icon {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.3rem;
        color: white;
        margin-right: 1rem;
        position: relative;
        overflow: hidden;
    }

    .mini-stats-icon::after {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 70%);
        animation: pulse-glow 2s infinite;
    }

    @keyframes pulse-glow {
        0%, 100% { opacity: 0; }
        50% { opacity: 1; }
    }

    .mini-stats-content {
        flex: 1;
    }

    .mini-stats-number {
        font-size: 1.8rem;
        font-weight: 700;
        color: #2d3748;
        line-height: 1;
        margin-bottom: 0.25rem;
    }

    .mini-stats-label {
        font-size: 0.85rem;
        color: #718096;
        font-weight: 500;
    }

    .mini-stats-trend {
        font-size: 1.2rem;
        margin-left: 0.5rem;
    }

    /* Pulse Animation for Alerts */
    .pulse {
        animation: pulse-alert 2s infinite;
    }

    @keyframes pulse-alert {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }

    /* Custom Colors */
    .bg-purple {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .btn-outline-purple {
        color: #667eea;
        border-color: #667eea;
    }

    .btn-outline-purple:hover {
        background-color: #667eea;
        border-color: #667eea;
        color: white;
    }

    /* Enhanced Actions Section */
    .actions-section {
        margin-bottom: 3rem;
    }

    .section-header {
        text-align: center;
        margin-bottom: 2rem;
    }

    .section-title {
        font-size: 2rem;
        font-weight: 700;
        color: #2d3748;
        margin-bottom: 0.5rem;
    }

    .section-subtitle {
        color: #718096;
        font-size: 1.1rem;
        margin-bottom: 0;
    }

    /* Enhanced Action Cards */
    .action-card-enhanced {
        background: white;
        border-radius: 20px;
        box-shadow: 0 8px 30px rgba(0,0,0,0.08);
        transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
        overflow: hidden;
        border: 1px solid rgba(0,0,0,0.05);
        position: relative;
        height: 100%;
    }

    .action-card-enhanced:hover {
        transform: translateY(-10px);
        box-shadow: 0 25px 50px rgba(0,0,0,0.15);
    }

    .action-card-enhanced::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--primary-gradient);
        transform: scaleX(0);
        transition: transform 0.4s ease;
    }

    .action-card-enhanced:hover::before {
        transform: scaleX(1);
    }

    .action-card-header {
        padding: 2rem 2rem 1rem;
        text-align: center;
        position: relative;
    }

    .action-icon-large {
        width: 80px;
        height: 80px;
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        color: white;
        margin: 0 auto 1rem;
        position: relative;
        overflow: hidden;
    }

    .action-icon-large::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
        animation: rotate-glow 6s linear infinite;
    }

    @keyframes rotate-glow {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .action-badge {
        position: absolute;
        top: 1rem;
        right: 1rem;
    }

    .action-badge .badge {
        font-size: 0.75rem;
        padding: 0.5rem 0.75rem;
        border-radius: 12px;
    }

    .action-card-body {
        padding: 0 2rem 2rem;
        text-align: center;
    }

    .action-title {
        font-size: 1.25rem;
        font-weight: 700;
        color: #2d3748;
        margin-bottom: 0.75rem;
    }

    .action-description {
        color: #718096;
        font-size: 0.9rem;
        margin-bottom: 1.5rem;
        line-height: 1.5;
    }

    .action-buttons {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
    }

    .btn-action {
        border-radius: 12px;
        font-weight: 500;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
    }

    .btn-action:hover {
        transform: translateY(-2px);
    }

    /* Secondary Action Cards */
    .secondary-action-card {
        background: white;
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.06);
        transition: all 0.3s ease;
        border: 1px solid rgba(0,0,0,0.05);
        height: 100%;
    }

    .secondary-action-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 12px 30px rgba(0,0,0,0.12);
    }

    .secondary-action-content {
        padding: 1.5rem;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .secondary-action-icon {
        width: 60px;
        height: 60px;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        flex-shrink: 0;
    }

    .secondary-action-text {
        flex: 1;
    }

    .secondary-action-title {
        font-size: 1rem;
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 0.25rem;
    }

    .secondary-action-desc {
        font-size: 0.85rem;
        color: #718096;
        margin-bottom: 0;
    }

    /* Gradient Backgrounds */
    .bg-gradient-dark {
        background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
    }

    .bg-gradient-purple {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    /* Modal Enhancements */
    .modal-content {
        border-radius: 15px;
        border: none;
        box-shadow: 0 20px 60px rgba(0,0,0,0.2);
    }

    .modal-header {
        border-radius: 15px 15px 0 0;
        border-bottom: none;
        padding: 1.5rem;
    }

    .modal-body {
        padding: 2rem;
    }

    .modal-footer {
        border-top: 1px solid #dee2e6;
        padding: 1.5rem;
    }

    /* Transfer Items */
    .transfer-item {
        background: #f8f9fa;
        border-radius: 10px;
        transition: all 0.3s ease;
    }

    .transfer-item:hover {
        background: #e9ecef;
    }

    /* List Group Enhancements */
    .list-group-item {
        border: none;
        border-bottom: 1px solid #dee2e6;
        padding: 1rem 0;
        transition: all 0.3s ease;
    }

    .list-group-item:hover {
        background-color: #f8f9fa;
        padding-left: 0.5rem;
    }

    .list-group-item:last-child {
        border-bottom: none;
    }

    .warehouse-type-icon {
        width: 60px;
        text-align: center;
    }

    /* Enhanced Responsive Design */
    @media (max-width: 1200px) {
        .header-icon {
            width: 60px;
            height: 60px;
            font-size: 1.5rem;
        }

        .header-text .header-title {
            font-size: 2rem;
        }

        .action-icon-large {
            width: 70px;
            height: 70px;
            font-size: 1.8rem;
        }
    }

    @media (max-width: 768px) {
        .warehouse-header {
            padding: 2rem 0;
        }

        .header-content {
            flex-direction: column;
            text-align: center;
        }

        .header-icon {
            margin-right: 0;
            margin-bottom: 1rem;
        }

        .header-actions {
            margin-top: 1.5rem;
        }

        .stats-card-modern {
            margin-bottom: 1rem;
        }

        .mini-stats-card {
            padding: 1rem;
        }

        .stats-number {
            font-size: 2rem;
        }

        .mini-stats-number {
            font-size: 1.5rem;
        }

        .action-card-enhanced {
            margin-bottom: 1.5rem;
        }

        .action-icon-large {
            width: 60px;
            height: 60px;
            font-size: 1.5rem;
        }

        .secondary-action-content {
            padding: 1rem;
        }

        .secondary-action-icon {
            width: 50px;
            height: 50px;
            font-size: 1.2rem;
        }

        .modal-dialog {
            margin: 1rem;
        }
    }

    @media (max-width: 576px) {
        .warehouse-header {
            padding: 1.5rem 0;
        }

        .header-text .header-title {
            font-size: 1.8rem;
        }

        .header-text .header-subtitle {
            font-size: 1rem;
        }

        .stats-icon-modern {
            width: 50px;
            height: 50px;
            font-size: 1.2rem;
        }

        .mini-stats-icon {
            width: 40px;
            height: 40px;
            font-size: 1rem;
        }

        .action-icon-large {
            width: 50px;
            height: 50px;
            font-size: 1.2rem;
        }

        .action-card-header {
            padding: 1.5rem 1rem 0.5rem;
        }

        .action-card-body {
            padding: 0 1rem 1.5rem;
        }

        .action-title {
            font-size: 1.1rem;
        }

        .action-description {
            font-size: 0.85rem;
        }

        .secondary-action-content {
            flex-direction: column;
            text-align: center;
            gap: 0.75rem;
        }

        .section-title {
            font-size: 1.5rem;
        }

        .section-subtitle {
            font-size: 1rem;
        }
    }

    /* Print Optimization */
    @media print {
        .warehouse-header .header-actions,
        .btn,
        .dropdown,
        .action-buttons {
            display: none !important;
        }

        .action-card-enhanced,
        .secondary-action-card,
        .stats-card-modern,
        .mini-stats-card {
            box-shadow: none !important;
            border: 1px solid #ddd !important;
            break-inside: avoid;
        }

        .warehouse-header {
            background: #f8f9fa !important;
            color: #000 !important;
        }
    }

    /* Accessibility Improvements */
    .btn:focus,
    .dropdown-toggle:focus {
        outline: 2px solid #667eea;
        outline-offset: 2px;
    }

    /* High Contrast Mode Support */
    @media (prefers-contrast: high) {
        .action-card-enhanced,
        .secondary-action-card,
        .stats-card-modern {
            border: 2px solid #000;
        }

        .dropdown-menu {
            border: 1px solid #000;
        }
    }

    /* Reduced Motion Support */
    @media (prefers-reduced-motion: reduce) {
        *,
        *::before,
        *::after {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
        }
    }

    /* Smooth Scrolling */
    html {
        scroll-behavior: smooth;
    }

    /* Enhanced Focus States */
    .btn:focus,
    .form-control:focus,
    .form-select:focus {
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        border-color: #667eea;
    }

    /* Loading States */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        backdrop-filter: blur(5px);
    }

    .loading-spinner {
        width: 50px;
        height: 50px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #667eea;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Print Styles */
    @media print {
        .warehouse-header,
        .header-actions,
        .btn,
        .dropdown {
            display: none !important;
        }

        .stats-card-modern,
        .mini-stats-card,
        .action-card {
            box-shadow: none !important;
            border: 1px solid #ddd !important;
        }
    }

    /* Loading Animation */
    .loading {
        opacity: 0.6;
        pointer-events: none;
    }

    .spinner-border-sm {
        width: 1rem;
        height: 1rem;
    }

    /* Success/Error Messages */
    .alert {
        border-radius: 10px;
        border: none;
    }

    /* Form Enhancements */
    .form-control, .form-select {
        border-radius: 8px;
        border: 1px solid #dee2e6;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    /* Button Enhancements */
    .btn {
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn:hover {
        transform: translateY(-1px);
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // Dashboard JavaScript Functions
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Set current datetime for forms
        const now = new Date();
        const currentDateTime = now.toISOString().slice(0, 16);
        document.querySelectorAll('input[type="datetime-local"]').forEach(input => {
            if (!input.value) {
                input.value = currentDateTime;
            }
        });

        // Auto-refresh dashboard every 5 minutes
        setInterval(refreshDashboard, 300000);
    });

    // Refresh Dashboard Data
    function refreshDashboard() {
        const refreshBtn = document.querySelector('[onclick="refreshDashboard()"]');
        const originalText = refreshBtn.innerHTML;

        refreshBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>جاري التحديث...';
        refreshBtn.disabled = true;

        // Simulate refresh (replace with actual AJAX call)
        setTimeout(() => {
            location.reload();
        }, 1000);
    }

    // Add Stock Functions
    function submitAddStock(button) {
        const form = document.getElementById('addStockForm');
        const formData = new FormData(form);

        // Validate form
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const submitBtn = button || event.target;
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>جاري الحفظ...';
        submitBtn.disabled = true;

        // AJAX call to submit data
        fetch('/warehouses/api/add-stock/', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': getCookie('csrftoken')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('تم إضافة المخزون بنجاح!', 'success');
                bootstrap.Modal.getInstance(document.getElementById('addStockModal')).hide();
                form.reset();
                setTimeout(() => location.reload(), 1500);
            } else {
                showAlert('حدث خطأ: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            showAlert('حدث خطأ في الاتصال', 'danger');
        })
        .finally(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });
    }

    // Issue Stock Functions
    function submitIssueStock(button) {
        const form = document.getElementById('issueStockForm');
        const formData = new FormData(form);

        // Validate form
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const submitBtn = button || event.target;
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>جاري الحفظ...';
        submitBtn.disabled = true;

        // AJAX call to submit data
        fetch('/warehouses/api/issue-stock/', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': getCookie('csrftoken')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('تم صرف المخزون بنجاح!', 'success');
                bootstrap.Modal.getInstance(document.getElementById('issueStockModal')).hide();
                form.reset();
                setTimeout(() => location.reload(), 1500);
            } else {
                showAlert('حدث خطأ: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            showAlert('حدث خطأ في الاتصال', 'danger');
        })
        .finally(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });
    }

    // Transfer Stock Functions
    function submitTransferStock(button) {
        const form = document.getElementById('transferStockForm');
        const formData = new FormData(form);

        // Validate form
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        // Validate that from and to warehouses are different
        const fromWarehouse = formData.get('from_warehouse');
        const toWarehouse = formData.get('to_warehouse');

        if (fromWarehouse === toWarehouse) {
            showAlert('لا يمكن التحويل من وإلى نفس المخزن', 'warning');
            return;
        }

        const submitBtn = button || event.target;
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>جاري الحفظ...';
        submitBtn.disabled = true;

        // AJAX call to submit data
        fetch('/warehouses/api/transfer-stock/', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': getCookie('csrftoken')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('تم إنشاء التحويل بنجاح!', 'success');
                bootstrap.Modal.getInstance(document.getElementById('transferStockModal')).hide();
                form.reset();
                setTimeout(() => location.reload(), 1500);
            } else {
                showAlert('حدث خطأ: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            showAlert('حدث خطأ في الاتصال', 'danger');
        })
        .finally(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });
    }

    // Add Transfer Item
    function addTransferItem() {
        const container = document.getElementById('transferItems');
        const newItem = container.querySelector('.transfer-item').cloneNode(true);

        // Clear values
        newItem.querySelectorAll('input, select').forEach(input => {
            input.value = '';
        });

        container.appendChild(newItem);
    }

    // Remove Transfer Item
    function removeTransferItem(button) {
        const container = document.getElementById('transferItems');
        if (container.children.length > 1) {
            button.closest('.transfer-item').remove();
        } else {
            showAlert('يجب أن يحتوي التحويل على منتج واحد على الأقل', 'warning');
        }
    }

    // Stock Adjustment Functions
    function submitAdjustment(button) {
        const form = document.getElementById('adjustmentForm');
        const formData = new FormData(form);

        // Validate form
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const submitBtn = button || event.target;
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>جاري الحفظ...';
        submitBtn.disabled = true;

        // AJAX call to submit data
        fetch('/warehouses/api/adjust-stock/', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': getCookie('csrftoken')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('تم إنشاء التسوية بنجاح!', 'success');
                bootstrap.Modal.getInstance(document.getElementById('adjustmentModal')).hide();
                form.reset();
                setTimeout(() => location.reload(), 1500);
            } else {
                showAlert('حدث خطأ: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            showAlert('حدث خطأ في الاتصال', 'danger');
        })
        .finally(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });
    }

    // Utility Functions
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    function showAlert(message, type) {
        const alertContainer = document.createElement('div');
        alertContainer.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertContainer.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alertContainer.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(alertContainer);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (alertContainer.parentNode) {
                alertContainer.remove();
            }
        }, 5000);
    }

    // Load products based on warehouse selection
    function loadProducts(warehouseSelect, productSelect) {
        const warehouseId = warehouseSelect.value;

        if (!warehouseId) {
            productSelect.innerHTML = '<option value="">اختر المنتج</option>';
            return;
        }

        productSelect.innerHTML = '<option value="">جاري التحميل...</option>';

        fetch(`/warehouses/api/warehouse-products/${warehouseId}/`)
        .then(response => response.json())
        .then(data => {
            productSelect.innerHTML = '<option value="">اختر المنتج</option>';
            data.products.forEach(product => {
                const option = document.createElement('option');
                option.value = product.id;
                option.textContent = `${product.name} (متاح: ${product.available_quantity})`;
                option.dataset.availableQuantity = product.available_quantity;
                productSelect.appendChild(option);
            });
        })
        .catch(error => {
            productSelect.innerHTML = '<option value="">خطأ في التحميل</option>';
        });
    }

    // Event listeners for warehouse changes
    document.addEventListener('change', function(e) {
        if (e.target.name === 'warehouse' && e.target.closest('#addStockModal, #issueStockModal')) {
            const modal = e.target.closest('.modal');
            const productSelect = modal.querySelector('select[name="product"]');
            loadProducts(e.target, productSelect);
        }

        if (e.target.name === 'from_warehouse' && e.target.closest('#transferStockModal')) {
            const modal = e.target.closest('.modal');
            const productSelects = modal.querySelectorAll('select[name="products[]"]');
            productSelects.forEach(select => {
                loadProducts(e.target, select);
            });
        }

        if (e.target.name === 'product' && e.target.closest('#issueStockModal')) {
            const selectedOption = e.target.selectedOptions[0];
            if (selectedOption && selectedOption.dataset.availableQuantity) {
                const availableInput = e.target.closest('.modal').querySelector('input[name="available_quantity"]');
                availableInput.value = selectedOption.dataset.availableQuantity;
            }
        }
    });
</script>
{% endblock %}
