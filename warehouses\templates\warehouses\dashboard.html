{% extends 'base.html' %}

{% block title %}لوحة تحكم المخازن - أوساريك{% endblock %}

{% block content %}
    <div class="page-header d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title">
                <i class="bi bi-speedometer2 me-2 text-primary"></i>لوحة تحكم المخازن
            </h1>
            <p class="page-subtitle">نظام إدارة شامل للمخازن والمخزون وحركة البضائع</p>
        </div>
        <div class="d-flex gap-2">
            <button class="btn btn-outline-primary" onclick="refreshDashboard()">
                <i class="bi bi-arrow-clockwise me-2"></i>تحديث البيانات
            </button>
            <div class="dropdown">
                <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="bi bi-plus-circle me-2"></i>إجراءات سريعة
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="{% url 'warehouses:warehouse_create' %}">
                        <i class="bi bi-building me-2"></i>إضافة مخزن جديد
                    </a></li>
                    <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#addStockModal">
                        <i class="bi bi-plus-square me-2"></i>إذن إضافة (زيادة مخزون)
                    </a></li>
                    <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#issueStockModal">
                        <i class="bi bi-dash-square me-2"></i>إذن صرف (نقص مخزون)
                    </a></li>
                    <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#transferStockModal">
                        <i class="bi bi-arrow-left-right me-2"></i>تحويل بين المخازن
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#adjustmentModal">
                        <i class="bi bi-gear me-2"></i>تسوية مخزون
                    </a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Advanced Statistics Cards -->
    <div class="row g-4 mb-4">
        <!-- Total Warehouses Card -->
        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card stats-card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-primary bg-gradient rounded-3 me-3">
                            <i class="bi bi-building text-white"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="text-muted mb-1">إجمالي المخازن</h6>
                            <h3 class="mb-0 fw-bold">{{ total_warehouses }}</h3>
                            <small class="text-success">
                                <i class="bi bi-arrow-up me-1"></i>نشط: {{ active_warehouses }}
                            </small>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="bi bi-three-dots"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{% url 'warehouses:warehouse_list' %}">عرض الكل</a></li>
                                <li><a class="dropdown-item" href="{% url 'warehouses:warehouse_create' %}">إضافة جديد</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Products Card -->
        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card stats-card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-success bg-gradient rounded-3 me-3">
                            <i class="bi bi-box text-white"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="text-muted mb-1">أصناف المنتجات</h6>
                            <h3 class="mb-0 fw-bold">{{ total_products }}</h3>
                            <small class="text-info">
                                <i class="bi bi-info-circle me-1"></i>في المخزون
                            </small>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-success dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="bi bi-three-dots"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{% url 'warehouses:stock_list' %}">عرض المخزون</a></li>
                                <li><a class="dropdown-item" href="{% url 'products:product_list' %}">إدارة المنتجات</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Stock Value Card -->
        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card stats-card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-info bg-gradient rounded-3 me-3">
                            <i class="bi bi-boxes text-white"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="text-muted mb-1">إجمالي الكميات</h6>
                            <h3 class="mb-0 fw-bold">{{ total_stock_value|floatformat:0 }}</h3>
                            <small class="text-primary">
                                <i class="bi bi-calculator me-1"></i>وحدة
                            </small>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-info dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="bi bi-three-dots"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{% url 'warehouses:stock_list' %}">تفاصيل المخزون</a></li>
                                <li><a class="dropdown-item" href="#">تقرير الكميات</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Low Stock Alert Card -->
        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card stats-card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-warning bg-gradient rounded-3 me-3">
                            <i class="bi bi-exclamation-triangle text-white"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="text-muted mb-1">تنبيهات المخزون</h6>
                            <h3 class="mb-0 fw-bold text-warning">{{ low_stock_items }}</h3>
                            <small class="text-danger">
                                <i class="bi bi-arrow-down me-1"></i>منخفض
                            </small>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-warning dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="bi bi-three-dots"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{% url 'warehouses:stock_list' %}?low_stock=1">عرض المنخفض</a></li>
                                <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#addStockModal">إذن إضافة</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Statistics Row -->
    <div class="row g-4 mb-4">
        <!-- Today's Movements -->
        <div class="col-xl-2 col-lg-4 col-md-6">
            <div class="card stats-mini-card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="stats-mini-icon bg-primary bg-gradient rounded-circle mx-auto mb-2">
                        <i class="bi bi-arrow-up-down text-white"></i>
                    </div>
                    <h5 class="mb-1 fw-bold">{{ today_movements }}</h5>
                    <small class="text-muted">حركات اليوم</small>
                </div>
            </div>
        </div>

        <!-- Pending Transfers -->
        <div class="col-xl-2 col-lg-4 col-md-6">
            <div class="card stats-mini-card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="stats-mini-icon bg-warning bg-gradient rounded-circle mx-auto mb-2">
                        <i class="bi bi-truck text-white"></i>
                    </div>
                    <h5 class="mb-1 fw-bold">{{ pending_transfers }}</h5>
                    <small class="text-muted">تحويلات معلقة</small>
                </div>
            </div>
        </div>

        <!-- Out of Stock -->
        <div class="col-xl-2 col-lg-4 col-md-6">
            <div class="card stats-mini-card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="stats-mini-icon bg-danger bg-gradient rounded-circle mx-auto mb-2">
                        <i class="bi bi-x-circle text-white"></i>
                    </div>
                    <h5 class="mb-1 fw-bold">{{ out_of_stock }}</h5>
                    <small class="text-muted">منتجات منتهية</small>
                </div>
            </div>
        </div>

        <!-- Total Locations -->
        <div class="col-xl-2 col-lg-4 col-md-6">
            <div class="card stats-mini-card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="stats-mini-icon bg-info bg-gradient rounded-circle mx-auto mb-2">
                        <i class="bi bi-geo-alt text-white"></i>
                    </div>
                    <h5 class="mb-1 fw-bold">{{ total_locations }}</h5>
                    <small class="text-muted">مواقع التخزين</small>
                </div>
            </div>
        </div>

        <!-- Reserved Stock -->
        <div class="col-xl-2 col-lg-4 col-md-6">
            <div class="card stats-mini-card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="stats-mini-icon bg-secondary bg-gradient rounded-circle mx-auto mb-2">
                        <i class="bi bi-lock text-white"></i>
                    </div>
                    <h5 class="mb-1 fw-bold">{{ reserved_stock }}</h5>
                    <small class="text-muted">مخزون محجوز</small>
                </div>
            </div>
        </div>

        <!-- Available Stock -->
        <div class="col-xl-2 col-lg-4 col-md-6">
            <div class="card stats-mini-card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="stats-mini-icon bg-success bg-gradient rounded-circle mx-auto mb-2">
                        <i class="bi bi-check-circle text-white"></i>
                    </div>
                    <h5 class="mb-1 fw-bold">{{ available_stock }}</h5>
                    <small class="text-muted">مخزون متاح</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Professional Quick Actions Cards -->
    <div class="row g-4 mb-4">
        <!-- Warehouse Management -->
        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card action-card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="action-icon bg-primary bg-gradient rounded-3 mx-auto mb-3">
                        <i class="bi bi-building text-white"></i>
                    </div>
                    <h5 class="mb-2 fw-bold">إدارة المخازن</h5>
                    <p class="text-muted small mb-3">إضافة وإدارة المخازن والمستودعات والمناطق</p>
                    <div class="d-grid gap-2">
                        <a href="{% url 'warehouses:warehouse_list' %}" class="btn btn-primary btn-sm">
                            <i class="bi bi-list me-2"></i>عرض المخازن
                        </a>
                        <a href="{% url 'warehouses:warehouse_create' %}" class="btn btn-outline-primary btn-sm">
                            <i class="bi bi-plus me-2"></i>إضافة مخزن
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Stock Management -->
        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card action-card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="action-icon bg-success bg-gradient rounded-3 mx-auto mb-3">
                        <i class="bi bi-boxes text-white"></i>
                    </div>
                    <h5 class="mb-2 fw-bold">إدارة المخزون</h5>
                    <p class="text-muted small mb-3">متابعة وإدارة مخزون المنتجات والكميات</p>
                    <div class="d-grid gap-2">
                        <a href="{% url 'warehouses:stock_list' %}" class="btn btn-success btn-sm">
                            <i class="bi bi-eye me-2"></i>عرض المخزون
                        </a>
                        <button class="btn btn-outline-success btn-sm" data-bs-toggle="modal" data-bs-target="#addStockModal">
                            <i class="bi bi-plus-square me-2"></i>إذن إضافة
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Stock Movements -->
        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card action-card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="action-icon bg-info bg-gradient rounded-3 mx-auto mb-3">
                        <i class="bi bi-arrow-up-down text-white"></i>
                    </div>
                    <h5 class="mb-2 fw-bold">حركة المخزون</h5>
                    <p class="text-muted small mb-3">متابعة حركات الوارد والصادر والتسويات</p>
                    <div class="d-grid gap-2">
                        <a href="{% url 'warehouses:stock_movements' %}" class="btn btn-info btn-sm">
                            <i class="bi bi-list me-2"></i>عرض الحركات
                        </a>
                        <button class="btn btn-outline-info btn-sm" data-bs-toggle="modal" data-bs-target="#issueStockModal">
                            <i class="bi bi-dash-square me-2"></i>إذن صرف
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Stock Transfers -->
        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card action-card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="action-icon bg-warning bg-gradient rounded-3 mx-auto mb-3">
                        <i class="bi bi-truck text-white"></i>
                    </div>
                    <h5 class="mb-2 fw-bold">تحويل المخزون</h5>
                    <p class="text-muted small mb-3">تحويل البضائع بين المخازن المختلفة</p>
                    <div class="d-grid gap-2">
                        <a href="{% url 'warehouses:stock_transfers' %}" class="btn btn-warning btn-sm">
                            <i class="bi bi-list me-2"></i>عرض التحويلات
                        </a>
                        <button class="btn btn-outline-warning btn-sm" data-bs-toggle="modal" data-bs-target="#transferStockModal">
                            <i class="bi bi-arrow-left-right me-2"></i>تحويل جديد
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Advanced Actions Row -->
    <div class="row g-4 mb-4">
        <!-- Stock Adjustment -->
        <div class="col-xl-4 col-lg-6">
            <div class="card action-card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="action-icon-sm bg-secondary bg-gradient rounded-3 me-3">
                            <i class="bi bi-gear text-white"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1 fw-bold">تسوية المخزون</h6>
                            <small class="text-muted">تصحيح الكميات والأخطاء</small>
                        </div>
                        <button class="btn btn-outline-secondary btn-sm" data-bs-toggle="modal" data-bs-target="#adjustmentModal">
                            <i class="bi bi-gear me-1"></i>تسوية
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Reports -->
        <div class="col-xl-4 col-lg-6">
            <div class="card action-card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="action-icon-sm bg-dark bg-gradient rounded-3 me-3">
                            <i class="bi bi-file-earmark-bar-graph text-white"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1 fw-bold">التقارير</h6>
                            <small class="text-muted">تقارير المخزون والحركات</small>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-outline-dark btn-sm dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="bi bi-file-text me-1"></i>تقارير
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#">تقرير المخزون</a></li>
                                <li><a class="dropdown-item" href="#">تقرير الحركات</a></li>
                                <li><a class="dropdown-item" href="#">تقرير التحويلات</a></li>
                                <li><a class="dropdown-item" href="#">تقرير المنخفض</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings -->
        <div class="col-xl-4 col-lg-6">
            <div class="card action-card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="action-icon-sm bg-purple bg-gradient rounded-3 me-3">
                            <i class="bi bi-sliders text-white"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1 fw-bold">إعدادات المخازن</h6>
                            <small class="text-muted">إعدادات النظام والتنبيهات</small>
                        </div>
                        <button class="btn btn-outline-purple btn-sm">
                            <i class="bi bi-sliders me-1"></i>إعدادات
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Analytics and Reports Section -->
    <div class="row g-4 mb-4">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-pie-chart me-2"></i>توزيع المخازن حسب النوع
                    </h5>
                </div>
                <div class="card-body">
                    {% if warehouse_stats %}
                        <div class="row g-3">
                            {% for stat in warehouse_stats %}
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center p-3 border rounded">
                                        <div class="warehouse-type-icon me-3">
                                            {% if stat.warehouse_type == 'main' %}
                                                <i class="bi bi-building text-primary" style="font-size: 2rem;"></i>
                                            {% elif stat.warehouse_type == 'branch' %}
                                                <i class="bi bi-geo-alt text-success" style="font-size: 2rem;"></i>
                                            {% elif stat.warehouse_type == 'temporary' %}
                                                <i class="bi bi-clock text-info" style="font-size: 2rem;"></i>
                                            {% elif stat.warehouse_type == 'damaged' %}
                                                <i class="bi bi-exclamation-triangle text-danger" style="font-size: 2rem;"></i>
                                            {% elif stat.warehouse_type == 'returns' %}
                                                <i class="bi bi-arrow-return-left text-warning" style="font-size: 2rem;"></i>
                                            {% endif %}
                                        </div>
                                        <div>
                                            <h6 class="mb-1">
                                                {% if stat.warehouse_type == 'main' %}مخزن رئيسي
                                                {% elif stat.warehouse_type == 'branch' %}مخزن فرع
                                                {% elif stat.warehouse_type == 'temporary' %}مخزن مؤقت
                                                {% elif stat.warehouse_type == 'damaged' %}مخزن تالف
                                                {% elif stat.warehouse_type == 'returns' %}مخزن مرتجعات
                                                {% endif %}
                                            </h6>
                                            <span class="badge bg-primary">{{ stat.count }} مخزن</span>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="bi bi-building text-muted" style="font-size: 3rem;"></i>
                            <h5 class="mt-3 text-muted">لا توجد مخازن</h5>
                            <p class="text-muted">لم يتم إضافة أي مخازن بعد.</p>
                            <a href="{% url 'warehouses:warehouse_create' %}" class="btn btn-primary">
                                <i class="bi bi-plus me-2"></i>إضافة أول مخزن
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-exclamation-triangle me-2"></i>تنبيهات المخزون
                    </h5>
                </div>
                <div class="card-body">
                    {% if reorder_items %}
                        <div class="list-group list-group-flush">
                            {% for item in reorder_items %}
                                <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <div>
                                        <h6 class="mb-1">{{ item.product.name }}</h6>
                                        <small class="text-muted">{{ item.warehouse.name }}</small>
                                    </div>
                                    <span class="badge bg-warning">{{ item.quantity }}</span>
                                </div>
                            {% endfor %}
                        </div>
                        <div class="mt-3">
                            <a href="{% url 'warehouses:stock_list' %}?low_stock=1" class="btn btn-outline-warning btn-sm w-100">
                                عرض جميع المنتجات المنخفضة
                            </a>
                        </div>
                    {% else %}
                        <div class="text-center py-3">
                            <i class="bi bi-check-circle text-success" style="font-size: 2rem;"></i>
                            <p class="text-muted mt-2 mb-0">جميع المنتجات في المستوى الطبيعي</p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Recent Movements -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-clock-history me-2"></i>آخر حركات المخزون
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_movements %}
                        <div class="list-group list-group-flush">
                            {% for movement in recent_movements %}
                                <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <div>
                                        <h6 class="mb-1">{{ movement.product.name }}</h6>
                                        <small class="text-muted">
                                            {{ movement.get_movement_type_display }} - {{ movement.warehouse.name }}
                                        </small>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge {% if movement.movement_type == 'in' %}bg-success{% else %}bg-danger{% endif %}">
                                            {% if movement.movement_type == 'in' %}+{% else %}-{% endif %}{{ movement.quantity }}
                                        </span>
                                        <br>
                                        <small class="text-muted">{{ movement.created_at|date:"m/d H:i" }}</small>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                        <div class="mt-3">
                            <a href="{% url 'warehouses:stock_movements' %}" class="btn btn-outline-primary btn-sm w-100">
                                عرض جميع الحركات
                            </a>
                        </div>
                    {% else %}
                        <div class="text-center py-3">
                            <i class="bi bi-arrow-left-right text-muted" style="font-size: 2rem;"></i>
                            <p class="text-muted mt-2 mb-0">لا توجد حركات حديثة</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Add Stock Modal (إذن إضافة) -->
    <div class="modal fade" id="addStockModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title">
                        <i class="bi bi-plus-square me-2"></i>إذن إضافة مخزون
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addStockForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">المخزن</label>
                                <select class="form-select" name="warehouse" required>
                                    <option value="">اختر المخزن</option>
                                    {% for warehouse in warehouses %}
                                        <option value="{{ warehouse.id }}">{{ warehouse.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">المنتج</label>
                                <select class="form-select" name="product" required>
                                    <option value="">اختر المنتج</option>
                                    <!-- سيتم تحميل المنتجات بـ AJAX -->
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label class="form-label">الكمية</label>
                                <input type="number" class="form-control" name="quantity" step="0.01" min="0" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">تكلفة الوحدة</label>
                                <input type="number" class="form-control" name="unit_cost" step="0.01" min="0">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">رقم المرجع</label>
                                <input type="text" class="form-control" name="reference_number" placeholder="رقم الفاتورة أو المرجع">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control" name="notes" rows="3" placeholder="ملاحظات إضافية..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-success" onclick="submitAddStock(this)">
                        <i class="bi bi-check-lg me-2"></i>تأكيد الإضافة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Issue Stock Modal (إذن صرف) -->
    <div class="modal fade" id="issueStockModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">
                        <i class="bi bi-dash-square me-2"></i>إذن صرف مخزون
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="issueStockForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">المخزن</label>
                                <select class="form-select" name="warehouse" required>
                                    <option value="">اختر المخزن</option>
                                    {% for warehouse in warehouses %}
                                        <option value="{{ warehouse.id }}">{{ warehouse.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">المنتج</label>
                                <select class="form-select" name="product" required>
                                    <option value="">اختر المنتج</option>
                                    <!-- سيتم تحميل المنتجات بـ AJAX -->
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label class="form-label">الكمية المطلوبة</label>
                                <input type="number" class="form-control" name="quantity" step="0.01" min="0" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">الكمية المتاحة</label>
                                <input type="number" class="form-control" name="available_quantity" readonly>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">رقم المرجع</label>
                                <input type="text" class="form-control" name="reference_number" placeholder="رقم الطلب أو المرجع">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">سبب الصرف</label>
                            <select class="form-select" name="reference_type" required>
                                <option value="">اختر السبب</option>
                                <option value="sale">بيع</option>
                                <option value="production">إنتاج</option>
                                <option value="damage">تلف</option>
                                <option value="return">مرتجع</option>
                                <option value="manual">يدوي</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control" name="notes" rows="3" placeholder="ملاحظات إضافية..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-danger" onclick="submitIssueStock(this)">
                        <i class="bi bi-check-lg me-2"></i>تأكيد الصرف
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Transfer Stock Modal (تحويل بين المخازن) -->
    <div class="modal fade" id="transferStockModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title">
                        <i class="bi bi-arrow-left-right me-2"></i>تحويل مخزون بين المخازن
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="transferStockForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">من المخزن</label>
                                <select class="form-select" name="from_warehouse" required>
                                    <option value="">اختر المخزن المرسل</option>
                                    {% for warehouse in warehouses %}
                                        <option value="{{ warehouse.id }}">{{ warehouse.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">إلى المخزن</label>
                                <select class="form-select" name="to_warehouse" required>
                                    <option value="">اختر المخزن المستقبل</option>
                                    {% for warehouse in warehouses %}
                                        <option value="{{ warehouse.id }}">{{ warehouse.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label class="form-label">رقم التحويل</label>
                                <input type="text" class="form-control" name="transfer_number" value="TR-{{ today|date:'Ymd' }}-001" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">تاريخ التحويل</label>
                                <input type="datetime-local" class="form-control" name="transfer_date" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">تاريخ الوصول المتوقع</label>
                                <input type="datetime-local" class="form-control" name="expected_arrival">
                            </div>
                        </div>

                        <!-- Transfer Items -->
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <label class="form-label mb-0">عناصر التحويل</label>
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="addTransferItem()">
                                    <i class="bi bi-plus me-1"></i>إضافة منتج
                                </button>
                            </div>
                            <div id="transferItems">
                                <div class="transfer-item border rounded p-3 mb-2">
                                    <div class="row">
                                        <div class="col-md-6 mb-2">
                                            <select class="form-select" name="products[]" required>
                                                <option value="">اختر المنتج</option>
                                                <!-- سيتم تحميل المنتجات بـ AJAX -->
                                            </select>
                                        </div>
                                        <div class="col-md-3 mb-2">
                                            <input type="number" class="form-control" name="quantities[]" placeholder="الكمية" step="0.01" min="0" required>
                                        </div>
                                        <div class="col-md-2 mb-2">
                                            <span class="form-control-plaintext text-muted">متاح: <span class="available-qty">0</span></span>
                                        </div>
                                        <div class="col-md-1 mb-2">
                                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeTransferItem(this)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control" name="notes" rows="3" placeholder="ملاحظات التحويل..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-warning" onclick="submitTransferStock(this)">
                        <i class="bi bi-check-lg me-2"></i>تأكيد التحويل
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Stock Adjustment Modal (تسوية المخزون) -->
    <div class="modal fade" id="adjustmentModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-secondary text-white">
                    <h5 class="modal-title">
                        <i class="bi bi-gear me-2"></i>تسوية المخزون
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="adjustmentForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">المخزن</label>
                                <select class="form-select" name="warehouse" required>
                                    <option value="">اختر المخزن</option>
                                    {% for warehouse in warehouses %}
                                        <option value="{{ warehouse.id }}">{{ warehouse.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">نوع التسوية</label>
                                <select class="form-select" name="adjustment_type" required>
                                    <option value="">اختر النوع</option>
                                    <option value="increase">زيادة</option>
                                    <option value="decrease">نقص</option>
                                    <option value="correction">تصحيح</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">رقم التسوية</label>
                                <input type="text" class="form-control" name="adjustment_number" value="ADJ-{{ today|date:'Ymd' }}-001" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">تاريخ التسوية</label>
                                <input type="datetime-local" class="form-control" name="adjustment_date" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">سبب التسوية</label>
                            <textarea class="form-control" name="reason" rows="3" placeholder="اذكر سبب التسوية..." required></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">معتمد من</label>
                            <input type="text" class="form-control" name="approved_by" placeholder="اسم المعتمد">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-secondary" onclick="submitAdjustment(this)">
                        <i class="bi bi-check-lg me-2"></i>تأكيد التسوية
                    </button>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block extra_css %}
<style>
    /* Professional Dashboard Styles */
    .stats-card {
        transition: all 0.3s ease;
        border-radius: 15px;
        overflow: hidden;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    }

    .stats-icon {
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
    }

    .stats-mini-card {
        transition: all 0.3s ease;
        border-radius: 12px;
    }

    .stats-mini-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }

    .stats-mini-icon {
        width: 45px;
        height: 45px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
    }

    .action-card {
        transition: all 0.3s ease;
        border-radius: 15px;
        overflow: hidden;
    }

    .action-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.12);
    }

    .action-icon {
        width: 70px;
        height: 70px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.8rem;
    }

    .action-icon-sm {
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
    }

    /* Custom Colors */
    .bg-purple {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .btn-outline-purple {
        color: #667eea;
        border-color: #667eea;
    }

    .btn-outline-purple:hover {
        background-color: #667eea;
        border-color: #667eea;
        color: white;
    }

    /* Dashboard Header */
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 15px;
        margin-bottom: 2rem;
        box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
    }

    .page-header .page-title {
        color: white !important;
        margin-bottom: 0.5rem;
    }

    .page-header .page-subtitle {
        color: rgba(255,255,255,0.9) !important;
        margin-bottom: 0;
    }

    /* Modal Enhancements */
    .modal-content {
        border-radius: 15px;
        border: none;
        box-shadow: 0 20px 60px rgba(0,0,0,0.2);
    }

    .modal-header {
        border-radius: 15px 15px 0 0;
        border-bottom: none;
        padding: 1.5rem;
    }

    .modal-body {
        padding: 2rem;
    }

    .modal-footer {
        border-top: 1px solid #dee2e6;
        padding: 1.5rem;
    }

    /* Transfer Items */
    .transfer-item {
        background: #f8f9fa;
        border-radius: 10px;
        transition: all 0.3s ease;
    }

    .transfer-item:hover {
        background: #e9ecef;
    }

    /* List Group Enhancements */
    .list-group-item {
        border: none;
        border-bottom: 1px solid #dee2e6;
        padding: 1rem 0;
        transition: all 0.3s ease;
    }

    .list-group-item:hover {
        background-color: #f8f9fa;
        padding-left: 0.5rem;
    }

    .list-group-item:last-child {
        border-bottom: none;
    }

    .warehouse-type-icon {
        width: 60px;
        text-align: center;
    }

    /* Responsive Adjustments */
    @media (max-width: 768px) {
        .page-header {
            padding: 1.5rem;
            text-align: center;
        }

        .stats-icon, .action-icon {
            width: 50px;
            height: 50px;
            font-size: 1.2rem;
        }

        .modal-dialog {
            margin: 1rem;
        }
    }

    /* Loading Animation */
    .loading {
        opacity: 0.6;
        pointer-events: none;
    }

    .spinner-border-sm {
        width: 1rem;
        height: 1rem;
    }

    /* Success/Error Messages */
    .alert {
        border-radius: 10px;
        border: none;
    }

    /* Form Enhancements */
    .form-control, .form-select {
        border-radius: 8px;
        border: 1px solid #dee2e6;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    /* Button Enhancements */
    .btn {
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn:hover {
        transform: translateY(-1px);
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // Dashboard JavaScript Functions
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Set current datetime for forms
        const now = new Date();
        const currentDateTime = now.toISOString().slice(0, 16);
        document.querySelectorAll('input[type="datetime-local"]').forEach(input => {
            if (!input.value) {
                input.value = currentDateTime;
            }
        });

        // Auto-refresh dashboard every 5 minutes
        setInterval(refreshDashboard, 300000);
    });

    // Refresh Dashboard Data
    function refreshDashboard() {
        const refreshBtn = document.querySelector('[onclick="refreshDashboard()"]');
        const originalText = refreshBtn.innerHTML;

        refreshBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>جاري التحديث...';
        refreshBtn.disabled = true;

        // Simulate refresh (replace with actual AJAX call)
        setTimeout(() => {
            location.reload();
        }, 1000);
    }

    // Add Stock Functions
    function submitAddStock(button) {
        const form = document.getElementById('addStockForm');
        const formData = new FormData(form);

        // Validate form
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const submitBtn = button || event.target;
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>جاري الحفظ...';
        submitBtn.disabled = true;

        // AJAX call to submit data
        fetch('/warehouses/api/add-stock/', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': getCookie('csrftoken')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('تم إضافة المخزون بنجاح!', 'success');
                bootstrap.Modal.getInstance(document.getElementById('addStockModal')).hide();
                form.reset();
                setTimeout(() => location.reload(), 1500);
            } else {
                showAlert('حدث خطأ: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            showAlert('حدث خطأ في الاتصال', 'danger');
        })
        .finally(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });
    }

    // Issue Stock Functions
    function submitIssueStock(button) {
        const form = document.getElementById('issueStockForm');
        const formData = new FormData(form);

        // Validate form
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const submitBtn = button || event.target;
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>جاري الحفظ...';
        submitBtn.disabled = true;

        // AJAX call to submit data
        fetch('/warehouses/api/issue-stock/', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': getCookie('csrftoken')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('تم صرف المخزون بنجاح!', 'success');
                bootstrap.Modal.getInstance(document.getElementById('issueStockModal')).hide();
                form.reset();
                setTimeout(() => location.reload(), 1500);
            } else {
                showAlert('حدث خطأ: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            showAlert('حدث خطأ في الاتصال', 'danger');
        })
        .finally(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });
    }

    // Transfer Stock Functions
    function submitTransferStock(button) {
        const form = document.getElementById('transferStockForm');
        const formData = new FormData(form);

        // Validate form
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        // Validate that from and to warehouses are different
        const fromWarehouse = formData.get('from_warehouse');
        const toWarehouse = formData.get('to_warehouse');

        if (fromWarehouse === toWarehouse) {
            showAlert('لا يمكن التحويل من وإلى نفس المخزن', 'warning');
            return;
        }

        const submitBtn = button || event.target;
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>جاري الحفظ...';
        submitBtn.disabled = true;

        // AJAX call to submit data
        fetch('/warehouses/api/transfer-stock/', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': getCookie('csrftoken')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('تم إنشاء التحويل بنجاح!', 'success');
                bootstrap.Modal.getInstance(document.getElementById('transferStockModal')).hide();
                form.reset();
                setTimeout(() => location.reload(), 1500);
            } else {
                showAlert('حدث خطأ: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            showAlert('حدث خطأ في الاتصال', 'danger');
        })
        .finally(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });
    }

    // Add Transfer Item
    function addTransferItem() {
        const container = document.getElementById('transferItems');
        const newItem = container.querySelector('.transfer-item').cloneNode(true);

        // Clear values
        newItem.querySelectorAll('input, select').forEach(input => {
            input.value = '';
        });

        container.appendChild(newItem);
    }

    // Remove Transfer Item
    function removeTransferItem(button) {
        const container = document.getElementById('transferItems');
        if (container.children.length > 1) {
            button.closest('.transfer-item').remove();
        } else {
            showAlert('يجب أن يحتوي التحويل على منتج واحد على الأقل', 'warning');
        }
    }

    // Stock Adjustment Functions
    function submitAdjustment(button) {
        const form = document.getElementById('adjustmentForm');
        const formData = new FormData(form);

        // Validate form
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const submitBtn = button || event.target;
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>جاري الحفظ...';
        submitBtn.disabled = true;

        // AJAX call to submit data
        fetch('/warehouses/api/adjust-stock/', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': getCookie('csrftoken')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('تم إنشاء التسوية بنجاح!', 'success');
                bootstrap.Modal.getInstance(document.getElementById('adjustmentModal')).hide();
                form.reset();
                setTimeout(() => location.reload(), 1500);
            } else {
                showAlert('حدث خطأ: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            showAlert('حدث خطأ في الاتصال', 'danger');
        })
        .finally(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });
    }

    // Utility Functions
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    function showAlert(message, type) {
        const alertContainer = document.createElement('div');
        alertContainer.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertContainer.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alertContainer.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(alertContainer);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (alertContainer.parentNode) {
                alertContainer.remove();
            }
        }, 5000);
    }

    // Load products based on warehouse selection
    function loadProducts(warehouseSelect, productSelect) {
        const warehouseId = warehouseSelect.value;

        if (!warehouseId) {
            productSelect.innerHTML = '<option value="">اختر المنتج</option>';
            return;
        }

        productSelect.innerHTML = '<option value="">جاري التحميل...</option>';

        fetch(`/warehouses/api/warehouse-products/${warehouseId}/`)
        .then(response => response.json())
        .then(data => {
            productSelect.innerHTML = '<option value="">اختر المنتج</option>';
            data.products.forEach(product => {
                const option = document.createElement('option');
                option.value = product.id;
                option.textContent = `${product.name} (متاح: ${product.available_quantity})`;
                option.dataset.availableQuantity = product.available_quantity;
                productSelect.appendChild(option);
            });
        })
        .catch(error => {
            productSelect.innerHTML = '<option value="">خطأ في التحميل</option>';
        });
    }

    // Event listeners for warehouse changes
    document.addEventListener('change', function(e) {
        if (e.target.name === 'warehouse' && e.target.closest('#addStockModal, #issueStockModal')) {
            const modal = e.target.closest('.modal');
            const productSelect = modal.querySelector('select[name="product"]');
            loadProducts(e.target, productSelect);
        }

        if (e.target.name === 'from_warehouse' && e.target.closest('#transferStockModal')) {
            const modal = e.target.closest('.modal');
            const productSelects = modal.querySelectorAll('select[name="products[]"]');
            productSelects.forEach(select => {
                loadProducts(e.target, select);
            });
        }

        if (e.target.name === 'product' && e.target.closest('#issueStockModal')) {
            const selectedOption = e.target.selectedOptions[0];
            if (selectedOption && selectedOption.dataset.availableQuantity) {
                const availableInput = e.target.closest('.modal').querySelector('input[name="available_quantity"]');
                availableInput.value = selectedOption.dataset.availableQuantity;
            }
        }
    });
</script>
{% endblock %}
