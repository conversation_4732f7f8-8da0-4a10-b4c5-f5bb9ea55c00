{% extends 'base.html' %}

{% block title %}المخازن والمستودعات - أوساريك{% endblock %}

{% block content %}
    <div class="page-header">
        <h1 class="page-title">المخازن والمستودعات</h1>
        <p class="page-subtitle">إدارة المخازن والمخزون وحركة البضائع</p>
    </div>

    <!-- Statistics Cards -->
    <div class="row g-4 mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="card text-center">
                <div class="card-body">
                    <i class="bi bi-building text-primary" style="font-size: 3rem;"></i>
                    <h4 class="mt-3 mb-1">{{ total_warehouses }}</h4>
                    <p class="text-muted mb-0">إجمالي المخازن</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card text-center">
                <div class="card-body">
                    <i class="bi bi-box text-success" style="font-size: 3rem;"></i>
                    <h4 class="mt-3 mb-1">{{ total_products }}</h4>
                    <p class="text-muted mb-0">أصناف المنتجات</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card text-center">
                <div class="card-body">
                    <i class="bi bi-boxes text-info" style="font-size: 3rem;"></i>
                    <h4 class="mt-3 mb-1">{{ total_stock_value|floatformat:0 }}</h4>
                    <p class="text-muted mb-0">إجمالي الكميات</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card text-center">
                <div class="card-body">
                    <i class="bi bi-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                    <h4 class="mt-3 mb-1">{{ low_stock_items }}</h4>
                    <p class="text-muted mb-0">منتجات منخفضة المخزون</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row g-4 mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="bi bi-building text-primary" style="font-size: 2.5rem;"></i>
                    <h5 class="mt-3 mb-2">إدارة المخازن</h5>
                    <p class="text-muted small mb-3">إضافة وإدارة المخازن والمستودعات</p>
                    <a href="{% url 'warehouses:warehouse_list' %}" class="btn btn-primary btn-sm">إدارة المخازن</a>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="bi bi-boxes text-success" style="font-size: 2.5rem;"></i>
                    <h5 class="mt-3 mb-2">إدارة المخزون</h5>
                    <p class="text-muted small mb-3">متابعة وإدارة مخزون المنتجات</p>
                    <a href="{% url 'warehouses:stock_list' %}" class="btn btn-success btn-sm">إدارة المخزون</a>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="bi bi-arrow-left-right text-info" style="font-size: 2.5rem;"></i>
                    <h5 class="mt-3 mb-2">حركة المخزون</h5>
                    <p class="text-muted small mb-3">متابعة حركات الوارد والصادر</p>
                    <a href="{% url 'warehouses:stock_movements' %}" class="btn btn-info btn-sm">حركة المخزون</a>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="bi bi-truck text-warning" style="font-size: 2.5rem;"></i>
                    <h5 class="mt-3 mb-2">تحويل المخزون</h5>
                    <p class="text-muted small mb-3">تحويل البضائع بين المخازن</p>
                    <a href="{% url 'warehouses:stock_transfers' %}" class="btn btn-warning btn-sm">تحويل المخزون</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Warehouse Types Distribution -->
    <div class="row g-4 mb-4">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-pie-chart me-2"></i>توزيع المخازن حسب النوع
                    </h5>
                </div>
                <div class="card-body">
                    {% if warehouse_stats %}
                        <div class="row g-3">
                            {% for stat in warehouse_stats %}
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center p-3 border rounded">
                                        <div class="warehouse-type-icon me-3">
                                            {% if stat.warehouse_type == 'main' %}
                                                <i class="bi bi-building text-primary" style="font-size: 2rem;"></i>
                                            {% elif stat.warehouse_type == 'branch' %}
                                                <i class="bi bi-geo-alt text-success" style="font-size: 2rem;"></i>
                                            {% elif stat.warehouse_type == 'temporary' %}
                                                <i class="bi bi-clock text-info" style="font-size: 2rem;"></i>
                                            {% elif stat.warehouse_type == 'damaged' %}
                                                <i class="bi bi-exclamation-triangle text-danger" style="font-size: 2rem;"></i>
                                            {% elif stat.warehouse_type == 'returns' %}
                                                <i class="bi bi-arrow-return-left text-warning" style="font-size: 2rem;"></i>
                                            {% endif %}
                                        </div>
                                        <div>
                                            <h6 class="mb-1">
                                                {% if stat.warehouse_type == 'main' %}مخزن رئيسي
                                                {% elif stat.warehouse_type == 'branch' %}مخزن فرع
                                                {% elif stat.warehouse_type == 'temporary' %}مخزن مؤقت
                                                {% elif stat.warehouse_type == 'damaged' %}مخزن تالف
                                                {% elif stat.warehouse_type == 'returns' %}مخزن مرتجعات
                                                {% endif %}
                                            </h6>
                                            <span class="badge bg-primary">{{ stat.count }} مخزن</span>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="bi bi-building text-muted" style="font-size: 3rem;"></i>
                            <h5 class="mt-3 text-muted">لا توجد مخازن</h5>
                            <p class="text-muted">لم يتم إضافة أي مخازن بعد.</p>
                            <a href="{% url 'warehouses:warehouse_create' %}" class="btn btn-primary">
                                <i class="bi bi-plus me-2"></i>إضافة أول مخزن
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-exclamation-triangle me-2"></i>تنبيهات المخزون
                    </h5>
                </div>
                <div class="card-body">
                    {% if reorder_items %}
                        <div class="list-group list-group-flush">
                            {% for item in reorder_items %}
                                <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <div>
                                        <h6 class="mb-1">{{ item.product.name }}</h6>
                                        <small class="text-muted">{{ item.warehouse.name }}</small>
                                    </div>
                                    <span class="badge bg-warning">{{ item.quantity }}</span>
                                </div>
                            {% endfor %}
                        </div>
                        <div class="mt-3">
                            <a href="{% url 'warehouses:stock_list' %}?low_stock=1" class="btn btn-outline-warning btn-sm w-100">
                                عرض جميع المنتجات المنخفضة
                            </a>
                        </div>
                    {% else %}
                        <div class="text-center py-3">
                            <i class="bi bi-check-circle text-success" style="font-size: 2rem;"></i>
                            <p class="text-muted mt-2 mb-0">جميع المنتجات في المستوى الطبيعي</p>
                        </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- Recent Movements -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-clock-history me-2"></i>آخر حركات المخزون
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_movements %}
                        <div class="list-group list-group-flush">
                            {% for movement in recent_movements %}
                                <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <div>
                                        <h6 class="mb-1">{{ movement.product.name }}</h6>
                                        <small class="text-muted">
                                            {{ movement.get_movement_type_display }} - {{ movement.warehouse.name }}
                                        </small>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge {% if movement.movement_type == 'in' %}bg-success{% else %}bg-danger{% endif %}">
                                            {% if movement.movement_type == 'in' %}+{% else %}-{% endif %}{{ movement.quantity }}
                                        </span>
                                        <br>
                                        <small class="text-muted">{{ movement.created_at|date:"m/d H:i" }}</small>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                        <div class="mt-3">
                            <a href="{% url 'warehouses:stock_movements' %}" class="btn btn-outline-primary btn-sm w-100">
                                عرض جميع الحركات
                            </a>
                        </div>
                    {% else %}
                        <div class="text-center py-3">
                            <i class="bi bi-arrow-left-right text-muted" style="font-size: 2rem;"></i>
                            <p class="text-muted mt-2 mb-0">لا توجد حركات حديثة</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block extra_css %}
<style>
    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }
    
    .list-group-item {
        border: none;
        border-bottom: 1px solid #dee2e6;
    }
    
    .list-group-item:last-child {
        border-bottom: none;
    }
    
    .warehouse-type-icon {
        width: 60px;
        text-align: center;
    }
</style>
{% endblock %}
