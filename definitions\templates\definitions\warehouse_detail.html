{% extends 'base.html' %}
{% load static %}

{% block title %}تفاصيل المخزن - {{ warehouse.name }}{% endblock %}

{% block extra_css %}
<style>
    .warehouse-detail-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 2rem 0;
        margin-bottom: 2rem;
        color: white;
        border-radius: 15px;
    }

    .detail-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .detail-section {
        margin-bottom: 2rem;
        padding-bottom: 1.5rem;
        border-bottom: 1px solid #eee;
    }

    .detail-section:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }

    .section-title {
        color: #495057;
        font-weight: 600;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #667eea;
        display: inline-block;
    }

    .detail-item {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
        padding: 0.75rem;
        background: #f8f9fa;
        border-radius: 8px;
    }

    .detail-label {
        font-weight: 600;
        color: #495057;
        min-width: 150px;
        margin-right: 1rem;
    }

    .detail-value {
        color: #212529;
        flex: 1;
    }

    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-weight: 500;
        font-size: 0.875rem;
    }

    .status-active {
        background: #d4edda;
        color: #155724;
    }

    .status-inactive {
        background: #f8d7da;
        color: #721c24;
    }

    .warehouse-type-badge {
        background: #e3f2fd;
        color: #1565c0;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-weight: 500;
        font-size: 0.875rem;
    }

    .action-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin-top: 2rem;
    }

    .btn-action {
        padding: 0.75rem 2rem;
        border-radius: 8px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .btn-edit {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border: none;
    }

    .btn-edit:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        color: white;
    }

    .btn-delete {
        background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
        color: white;
        border: none;
    }

    .btn-delete:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
        color: white;
    }

    .empty-value {
        color: #6c757d;
        font-style: italic;
    }

    .warehouse-icon {
        font-size: 4rem;
        color: #667eea;
        margin-bottom: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<!-- Detail Header -->
<div class="warehouse-detail-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-building me-2"></i>
                    {{ warehouse.name }}
                </h1>
                <p class="mb-0 opacity-75">تفاصيل المخزن - {{ warehouse.code }}</p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'definitions:warehouse_list' %}" class="btn btn-light">
                    <i class="bi bi-arrow-right me-1"></i>العودة للقائمة
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Detail Content -->
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="detail-card">
                <!-- Basic Information -->
                <div class="detail-section">
                    <h4 class="section-title">
                        <i class="bi bi-info-circle me-2"></i>المعلومات الأساسية
                    </h4>
                    
                    <div class="detail-item">
                        <div class="detail-label">كود المخزن:</div>
                        <div class="detail-value">
                            <strong>{{ warehouse.code }}</strong>
                        </div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-label">اسم المخزن:</div>
                        <div class="detail-value">{{ warehouse.name }}</div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-label">نوع المخزن:</div>
                        <div class="detail-value">
                            <span class="warehouse-type-badge">
                                {{ warehouse.get_warehouse_type_display }}
                            </span>
                        </div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-label">مدير المخزن:</div>
                        <div class="detail-value">
                            {% if warehouse.manager_name %}
                                {{ warehouse.manager_name }}
                            {% else %}
                                <span class="empty-value">غير محدد</span>
                            {% endif %}
                        </div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-label">الحالة:</div>
                        <div class="detail-value">
                            <span class="status-badge {% if warehouse.is_active %}status-active{% else %}status-inactive{% endif %}">
                                <i class="bi bi-{% if warehouse.is_active %}check-circle{% else %}x-circle{% endif %} me-1"></i>
                                {{ warehouse.is_active|yesno:"نشط,غير نشط" }}
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="detail-section">
                    <h4 class="section-title">
                        <i class="bi bi-telephone me-2"></i>معلومات الاتصال والموقع
                    </h4>
                    
                    <div class="detail-item">
                        <div class="detail-label">رقم الهاتف:</div>
                        <div class="detail-value">
                            {% if warehouse.phone %}
                                <a href="tel:{{ warehouse.phone }}" class="text-decoration-none">
                                    <i class="bi bi-telephone me-1"></i>{{ warehouse.phone }}
                                </a>
                            {% else %}
                                <span class="empty-value">غير محدد</span>
                            {% endif %}
                        </div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-label">العنوان:</div>
                        <div class="detail-value">
                            {% if warehouse.address %}
                                <i class="bi bi-geo-alt me-1"></i>{{ warehouse.address }}
                            {% else %}
                                <span class="empty-value">غير محدد</span>
                            {% endif %}
                        </div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-label">الوصف:</div>
                        <div class="detail-value">
                            {% if warehouse.description %}
                                {{ warehouse.description }}
                            {% else %}
                                <span class="empty-value">لا يوجد وصف</span>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Settings -->
                <div class="detail-section">
                    <h4 class="section-title">
                        <i class="bi bi-gear me-2"></i>إعدادات المخزن
                    </h4>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="detail-item">
                                <div class="detail-label">المخزون السالب:</div>
                                <div class="detail-value">
                                    <i class="bi bi-{% if warehouse.allow_negative_stock %}check-circle text-success{% else %}x-circle text-danger{% endif %} me-1"></i>
                                    {{ warehouse.allow_negative_stock|yesno:"مسموح,غير مسموح" }}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="detail-item">
                                <div class="detail-label">إعادة الطلب التلقائي:</div>
                                <div class="detail-value">
                                    <i class="bi bi-{% if warehouse.auto_reorder %}check-circle text-success{% else %}x-circle text-danger{% endif %} me-1"></i>
                                    {{ warehouse.auto_reorder|yesno:"مفعل,غير مفعل" }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Information -->
                <div class="detail-section">
                    <h4 class="section-title">
                        <i class="bi bi-clock-history me-2"></i>معلومات النظام
                    </h4>
                    
                    <div class="detail-item">
                        <div class="detail-label">تاريخ الإنشاء:</div>
                        <div class="detail-value">
                            <i class="bi bi-calendar me-1"></i>
                            {{ warehouse.created_at|date:"d/m/Y H:i" }}
                        </div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-label">آخر تحديث:</div>
                        <div class="detail-value">
                            <i class="bi bi-clock me-1"></i>
                            {{ warehouse.updated_at|date:"d/m/Y H:i" }}
                        </div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-label">أنشئ بواسطة:</div>
                        <div class="detail-value">
                            <i class="bi bi-person me-1"></i>
                            {{ warehouse.created_by.get_full_name|default:warehouse.created_by.username }}
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="action-buttons">
                    <a href="{% url 'definitions:warehouse_edit' warehouse.id %}" class="btn-action btn-edit">
                        <i class="bi bi-pencil me-2"></i>تعديل المخزن
                    </a>
                    <a href="{% url 'definitions:warehouse_delete' warehouse.id %}" class="btn-action btn-delete" onclick="return confirm('هل أنت متأكد من حذف هذا المخزن؟\n\nتحذير: سيتم حذف جميع البيانات المرتبطة بهذا المخزن!')">
                        <i class="bi bi-trash me-2"></i>حذف المخزن
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Information -->
    <div class="row">
        <div class="col-12">
            <div class="alert alert-info">
                <h6><i class="bi bi-info-circle me-2"></i>معلومات إضافية:</h6>
                <ul class="mb-0">
                    <li>يمكنك تعديل جميع بيانات المخزن عدا الكود</li>
                    <li>إلغاء تفعيل المخزن يمنع استخدامه في العمليات الجديدة</li>
                    <li>حذف المخزن سيؤثر على جميع البيانات المرتبطة به</li>
                    <li>يمكنك عرض تقارير المخزون من قسم إدارة المخازن</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
