{% extends 'base.html' %}
{% load static %}

{% block title %}لوحة تحكم التعريفات{% endblock %}

{% block extra_css %}
<style>
    /* Definitions Dashboard Styles */
    :root {
        --definitions-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --definitions-success: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        --definitions-warning: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --definitions-danger: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
        --definitions-info: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        --definitions-secondary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .definitions-header {
        background: var(--definitions-primary);
        padding: 3rem 0;
        margin-bottom: 3rem;
        position: relative;
        overflow: visible;
        color: white;
    }

    .definitions-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="definitions-pattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23definitions-pattern)"/></svg>');
        opacity: 0.3;
    }

    .header-content {
        position: relative;
        z-index: 2;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .header-info {
        display: flex;
        align-items: center;
    }

    .header-icon {
        width: 80px;
        height: 80px;
        background: rgba(255,255,255,0.2);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.3);
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 2rem;
        font-size: 2rem;
        color: white;
    }

    .header-text h1 {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .header-text p {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 0;
    }

    /* Definition Cards Grid */
    .definitions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-bottom: 3rem;
    }

    .definition-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 8px 30px rgba(0,0,0,0.08);
        transition: all 0.4s ease;
        overflow: hidden;
        border: 1px solid rgba(0,0,0,0.05);
        position: relative;
        height: 100%;
    }

    .definition-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }

    .definition-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--card-gradient);
        transition: all 0.3s ease;
    }

    .definition-card.primary::before { background: var(--definitions-primary); }
    .definition-card.success::before { background: var(--definitions-success); }
    .definition-card.warning::before { background: var(--definitions-warning); }
    .definition-card.danger::before { background: var(--definitions-danger); }
    .definition-card.info::before { background: var(--definitions-info); }
    .definition-card.secondary::before { background: var(--definitions-secondary); }

    .card-header-custom {
        padding: 2rem 2rem 1rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .card-icon {
        width: 60px;
        height: 60px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        position: relative;
        overflow: hidden;
    }

    .card-icon::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
        animation: definitions-shimmer 4s infinite;
    }

    @keyframes definitions-shimmer {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .card-count {
        font-size: 2rem;
        font-weight: 700;
        color: #2d3748;
        text-align: center;
    }

    .card-body-custom {
        padding: 0 2rem 2rem;
    }

    .card-title-custom {
        font-size: 1.25rem;
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 0.5rem;
    }

    .card-description {
        color: #718096;
        font-size: 0.9rem;
        margin-bottom: 1.5rem;
        line-height: 1.5;
    }

    .card-actions {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    .btn-card-action {
        background: rgba(102, 126, 234, 0.1);
        border: 1px solid rgba(102, 126, 234, 0.2);
        color: #667eea;
        border-radius: 8px;
        padding: 0.5rem 1rem;
        font-size: 0.85rem;
        font-weight: 500;
        transition: all 0.3s ease;
        text-decoration: none;
    }

    .btn-card-action:hover {
        background: #667eea;
        color: white;
        transform: translateY(-2px);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .definitions-header {
            padding: 2rem 0;
        }

        .header-content {
            flex-direction: column;
            text-align: center;
        }

        .header-icon {
            margin-left: 0;
            margin-bottom: 1rem;
        }

        .header-text h1 {
            font-size: 2rem;
        }

        .definitions-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .card-header-custom {
            padding: 1.5rem 1rem 1rem;
        }

        .card-body-custom {
            padding: 0 1rem 1.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Definitions Header -->
<div class="definitions-header">
    <div class="container-fluid">
        <div class="header-content">
            <div class="header-info">
                <div class="header-icon">
                    <i class="bi bi-gear"></i>
                </div>
                <div class="header-text">
                    <h1>لوحة تحكم التعريفات</h1>
                    <p>إدارة شاملة لجميع تعريفات النظام والبيانات الأساسية</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Definitions Grid -->
<div class="container-fluid">
    <div class="definitions-grid">
        <!-- تعريف المخازن -->
        <div class="definition-card primary">
            <div class="card-header-custom">
                <div class="card-icon" style="background: var(--definitions-primary);">
                    <i class="bi bi-building"></i>
                </div>
                <div class="card-count">{{ stats.warehouses }}</div>
            </div>
            <div class="card-body-custom">
                <h5 class="card-title-custom">تعريف المخازن</h5>
                <p class="card-description">إدارة وتعريف المخازن والمستودعات وأنواعها المختلفة</p>
                <div class="card-actions">
                    <a href="{% url 'definitions:warehouse_list' %}" class="btn-card-action">
                        <i class="bi bi-list me-1"></i>عرض الكل
                    </a>
                    <a href="#" class="btn-card-action">
                        <i class="bi bi-plus me-1"></i>إضافة جديد
                    </a>
                </div>
            </div>
        </div>

        <!-- فئات الأصناف -->
        <div class="definition-card success">
            <div class="card-header-custom">
                <div class="card-icon" style="background: var(--definitions-success);">
                    <i class="bi bi-tags"></i>
                </div>
                <div class="card-count">{{ stats.categories }}</div>
            </div>
            <div class="card-body-custom">
                <h5 class="card-title-custom">فئات الأصناف</h5>
                <p class="card-description">تصنيف وتنظيم الأصناف في مجموعات وفئات</p>
                <div class="card-actions">
                    <a href="{% url 'definitions:category_list' %}" class="btn-card-action">
                        <i class="bi bi-list me-1"></i>عرض الكل
                    </a>
                    <a href="#" class="btn-card-action">
                        <i class="bi bi-plus me-1"></i>إضافة جديد
                    </a>
                </div>
            </div>
        </div>

        <!-- تعريف الأصناف -->
        <div class="definition-card warning">
            <div class="card-header-custom">
                <div class="card-icon" style="background: var(--definitions-warning);">
                    <i class="bi bi-box"></i>
                </div>
                <div class="card-count">{{ stats.products }}</div>
            </div>
            <div class="card-body-custom">
                <h5 class="card-title-custom">تعريف الأصناف</h5>
                <p class="card-description">إدارة المنتجات والخدمات والمواد الخام</p>
                <div class="card-actions">
                    <a href="{% url 'definitions:product_list' %}" class="btn-card-action">
                        <i class="bi bi-list me-1"></i>عرض الكل
                    </a>
                    <a href="#" class="btn-card-action">
                        <i class="bi bi-plus me-1"></i>إضافة جديد
                    </a>
                </div>
            </div>
        </div>

        <!-- الأشخاص -->
        <div class="definition-card danger">
            <div class="card-header-custom">
                <div class="card-icon" style="background: var(--definitions-danger);">
                    <i class="bi bi-people"></i>
                </div>
                <div class="card-count">{{ stats.persons }}</div>
            </div>
            <div class="card-body-custom">
                <h5 class="card-title-custom">الأشخاص</h5>
                <p class="card-description">إدارة العملاء والموردين والموظفين</p>
                <div class="card-actions">
                    <a href="#" class="btn-card-action">
                        <i class="bi bi-list me-1"></i>عرض الكل
                    </a>
                    <a href="#" class="btn-card-action">
                        <i class="bi bi-plus me-1"></i>إضافة جديد
                    </a>
                </div>
            </div>
        </div>

        <!-- البنوك -->
        <div class="definition-card info">
            <div class="card-header-custom">
                <div class="card-icon" style="background: var(--definitions-info);">
                    <i class="bi bi-bank"></i>
                </div>
                <div class="card-count">{{ stats.banks }}</div>
            </div>
            <div class="card-body-custom">
                <h5 class="card-title-custom">البنوك</h5>
                <p class="card-description">تعريف البنوك والحسابات المصرفية</p>
                <div class="card-actions">
                    <a href="#" class="btn-card-action">
                        <i class="bi bi-list me-1"></i>عرض الكل
                    </a>
                    <a href="#" class="btn-card-action">
                        <i class="bi bi-plus me-1"></i>إضافة جديد
                    </a>
                </div>
            </div>
        </div>

        <!-- العملات -->
        <div class="definition-card secondary">
            <div class="card-header-custom">
                <div class="card-icon" style="background: var(--definitions-secondary);">
                    <i class="bi bi-currency-exchange"></i>
                </div>
                <div class="card-count">{{ stats.currencies }}</div>
            </div>
            <div class="card-body-custom">
                <h5 class="card-title-custom">العملات</h5>
                <p class="card-description">إدارة العملات وأسعار الصرف</p>
                <div class="card-actions">
                    <a href="#" class="btn-card-action">
                        <i class="bi bi-list me-1"></i>عرض الكل
                    </a>
                    <a href="#" class="btn-card-action">
                        <i class="bi bi-plus me-1"></i>إضافة جديد
                    </a>
                </div>
            </div>
        </div>

        <!-- الخزائن -->
        <div class="definition-card primary">
            <div class="card-header-custom">
                <div class="card-icon" style="background: var(--definitions-primary);">
                    <i class="bi bi-safe"></i>
                </div>
                <div class="card-count">{{ stats.cashboxes }}</div>
            </div>
            <div class="card-body-custom">
                <h5 class="card-title-custom">الخزائن</h5>
                <p class="card-description">إدارة الخزائن النقدية والأرصدة</p>
                <div class="card-actions">
                    <a href="#" class="btn-card-action">
                        <i class="bi bi-list me-1"></i>عرض الكل
                    </a>
                    <a href="#" class="btn-card-action">
                        <i class="bi bi-plus me-1"></i>إضافة جديد
                    </a>
                </div>
            </div>
        </div>

        <!-- مجموعات الأصول -->
        <div class="definition-card success">
            <div class="card-header-custom">
                <div class="card-icon" style="background: var(--definitions-success);">
                    <i class="bi bi-diagram-3"></i>
                </div>
                <div class="card-count">{{ stats.asset_groups }}</div>
            </div>
            <div class="card-body-custom">
                <h5 class="card-title-custom">مجموعات الأصول</h5>
                <p class="card-description">تصنيف الأصول الثابتة ومعدلات الإهلاك</p>
                <div class="card-actions">
                    <a href="#" class="btn-card-action">
                        <i class="bi bi-list me-1"></i>عرض الكل
                    </a>
                    <a href="#" class="btn-card-action">
                        <i class="bi bi-plus me-1"></i>إضافة جديد
                    </a>
                </div>
            </div>
        </div>

        <!-- أنواع المصروفات -->
        <div class="definition-card warning">
            <div class="card-header-custom">
                <div class="card-icon" style="background: var(--definitions-warning);">
                    <i class="bi bi-arrow-down-circle"></i>
                </div>
                <div class="card-count">{{ stats.expense_types }}</div>
            </div>
            <div class="card-body-custom">
                <h5 class="card-title-custom">أنواع المصروفات</h5>
                <p class="card-description">تصنيف وإدارة أنواع المصروفات المختلفة</p>
                <div class="card-actions">
                    <a href="#" class="btn-card-action">
                        <i class="bi bi-list me-1"></i>عرض الكل
                    </a>
                    <a href="#" class="btn-card-action">
                        <i class="bi bi-plus me-1"></i>إضافة جديد
                    </a>
                </div>
            </div>
        </div>

        <!-- أنواع الإيرادات -->
        <div class="definition-card danger">
            <div class="card-header-custom">
                <div class="card-icon" style="background: var(--definitions-danger);">
                    <i class="bi bi-arrow-up-circle"></i>
                </div>
                <div class="card-count">{{ stats.revenue_types }}</div>
            </div>
            <div class="card-body-custom">
                <h5 class="card-title-custom">أنواع الإيرادات</h5>
                <p class="card-description">تصنيف وإدارة أنواع الإيرادات المختلفة</p>
                <div class="card-actions">
                    <a href="#" class="btn-card-action">
                        <i class="bi bi-list me-1"></i>عرض الكل
                    </a>
                    <a href="#" class="btn-card-action">
                        <i class="bi bi-plus me-1"></i>إضافة جديد
                    </a>
                </div>
            </div>
        </div>

        <!-- مراكز الربحية -->
        <div class="definition-card info">
            <div class="card-header-custom">
                <div class="card-icon" style="background: var(--definitions-info);">
                    <i class="bi bi-bullseye"></i>
                </div>
                <div class="card-count">{{ stats.profit_centers }}</div>
            </div>
            <div class="card-body-custom">
                <h5 class="card-title-custom">مراكز الربحية</h5>
                <p class="card-description">إدارة مراكز التكلفة والربح والأهداف</p>
                <div class="card-actions">
                    <a href="#" class="btn-card-action">
                        <i class="bi bi-list me-1"></i>عرض الكل
                    </a>
                    <a href="#" class="btn-card-action">
                        <i class="bi bi-plus me-1"></i>إضافة جديد
                    </a>
                </div>
            </div>
        </div>

        <!-- الطابعات -->
        <div class="definition-card secondary">
            <div class="card-header-custom">
                <div class="card-icon" style="background: var(--definitions-secondary);">
                    <i class="bi bi-printer"></i>
                </div>
                <div class="card-count">{{ stats.printers }}</div>
            </div>
            <div class="card-body-custom">
                <h5 class="card-title-custom">الطابعات</h5>
                <p class="card-description">إعدادات وتكوين الطابعات والأجهزة</p>
                <div class="card-actions">
                    <a href="#" class="btn-card-action">
                        <i class="bi bi-list me-1"></i>عرض الكل
                    </a>
                    <a href="#" class="btn-card-action">
                        <i class="bi bi-plus me-1"></i>إضافة جديد
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function refreshDashboard() {
    location.reload();
}
</script>
{% endblock %}
