from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal

# ===== تعريف المخازن =====
class WarehouseDefinition(models.Model):
    """تعريف المخازن"""
    WAREHOUSE_TYPES = [
        ('main', 'مخزن رئيسي'),
        ('branch', 'مخزن فرعي'),
        ('raw_materials', 'مخزن مواد خام'),
        ('finished_goods', 'مخزن منتجات تامة'),
        ('damaged', 'مخزن تالف'),
        ('quarantine', 'مخزن حجر صحي'),
    ]

    code = models.CharField(max_length=20, unique=True, verbose_name="كود المخزن")
    name = models.CharField(max_length=100, verbose_name="اسم المخزن")
    name_en = models.Char<PERSON><PERSON>(max_length=100, blank=True, verbose_name="الاسم بالإنجليزية")
    warehouse_type = models.CharField(max_length=20, choices=WAREHOUSE_TYPES, verbose_name="نوع المخزن")

    address = models.TextField(blank=True, verbose_name="العنوان")
    phone = models.CharField(max_length=20, blank=True, verbose_name="الهاتف")
    manager_name = models.CharField(max_length=100, blank=True, verbose_name="اسم المدير")

    is_active = models.BooleanField(default=True, verbose_name="نشط")
    allow_negative_stock = models.BooleanField(default=False, verbose_name="السماح بالمخزون السالب")
    auto_reorder = models.BooleanField(default=False, verbose_name="إعادة الطلب التلقائي")

    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشئ بواسطة")

    class Meta:
        verbose_name = "تعريف مخزن"
        verbose_name_plural = "تعريفات المخازن"
        ordering = ['code']

    def __str__(self):
        return f"{self.code} - {self.name}"

# ===== فئات الأصناف =====
class ProductCategory(models.Model):
    """فئات الأصناف"""
    code = models.CharField(max_length=20, unique=True, verbose_name="كود الفئة")
    name = models.CharField(max_length=100, verbose_name="اسم الفئة")
    name_en = models.CharField(max_length=100, blank=True, verbose_name="الاسم بالإنجليزية")
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, verbose_name="الفئة الأب")

    description = models.TextField(blank=True, verbose_name="الوصف")
    image = models.ImageField(upload_to='categories/', blank=True, verbose_name="صورة الفئة")

    is_active = models.BooleanField(default=True, verbose_name="نشط")
    sort_order = models.PositiveIntegerField(default=0, verbose_name="ترتيب العرض")

    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشئ بواسطة")

    class Meta:
        verbose_name = "فئة صنف"
        verbose_name_plural = "فئات الأصناف"
        ordering = ['sort_order', 'name']

    def __str__(self):
        return f"{self.code} - {self.name}"

    def get_full_path(self):
        """الحصول على المسار الكامل للفئة"""
        if self.parent:
            return f"{self.parent.get_full_path()} > {self.name}"
        return self.name

# ===== تعريف الأصناف =====
class ProductDefinition(models.Model):
    """تعريف الأصناف"""
    PRODUCT_TYPES = [
        ('product', 'منتج'),
        ('service', 'خدمة'),
        ('raw_material', 'مادة خام'),
        ('finished_good', 'منتج تام'),
        ('semi_finished', 'منتج نصف مصنع'),
        ('consumable', 'مستهلكات'),
    ]

    UNIT_TYPES = [
        ('piece', 'قطعة'),
        ('kg', 'كيلوجرام'),
        ('gram', 'جرام'),
        ('liter', 'لتر'),
        ('meter', 'متر'),
        ('box', 'صندوق'),
        ('carton', 'كرتونة'),
        ('dozen', 'دستة'),
    ]

    code = models.CharField(max_length=50, unique=True, verbose_name="كود الصنف")
    barcode = models.CharField(max_length=50, blank=True, unique=True, verbose_name="الباركود")
    name = models.CharField(max_length=200, verbose_name="اسم الصنف")
    name_en = models.CharField(max_length=200, blank=True, verbose_name="الاسم بالإنجليزية")

    category = models.ForeignKey(ProductCategory, on_delete=models.CASCADE, verbose_name="الفئة")
    product_type = models.CharField(max_length=20, choices=PRODUCT_TYPES, verbose_name="نوع الصنف")

    description = models.TextField(blank=True, verbose_name="الوصف")
    specifications = models.TextField(blank=True, verbose_name="المواصفات")
    image = models.ImageField(upload_to='products/', blank=True, verbose_name="صورة الصنف")

    # وحدات القياس
    main_unit = models.CharField(max_length=20, choices=UNIT_TYPES, verbose_name="الوحدة الأساسية")
    sub_unit = models.CharField(max_length=20, choices=UNIT_TYPES, blank=True, verbose_name="الوحدة الفرعية")
    conversion_factor = models.DecimalField(max_digits=10, decimal_places=4, default=1, verbose_name="معامل التحويل")

    # الأسعار
    cost_price = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="سعر التكلفة")
    selling_price = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="سعر البيع")
    wholesale_price = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="سعر الجملة")

    # حدود المخزون
    minimum_stock = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name="الحد الأدنى للمخزون")
    maximum_stock = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name="الحد الأقصى للمخزون")
    reorder_point = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name="نقطة إعادة الطلب")

    # الضرائب والخصومات
    tax_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0, verbose_name="معدل الضريبة %")
    discount_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0, verbose_name="معدل الخصم %")

    # الحالة
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    is_sellable = models.BooleanField(default=True, verbose_name="قابل للبيع")
    is_purchasable = models.BooleanField(default=True, verbose_name="قابل للشراء")
    track_inventory = models.BooleanField(default=True, verbose_name="تتبع المخزون")

    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشئ بواسطة")

    class Meta:
        verbose_name = "تعريف صنف"
        verbose_name_plural = "تعريفات الأصناف"
        ordering = ['code']

    def __str__(self):
        return f"{self.code} - {self.name}"

# ===== أماكن الأصناف بالمخازن =====
class ProductLocation(models.Model):
    """أماكن الأصناف بالمخازن"""
    warehouse = models.ForeignKey(WarehouseDefinition, on_delete=models.CASCADE, verbose_name="المخزن")
    product = models.ForeignKey(ProductDefinition, on_delete=models.CASCADE, verbose_name="الصنف")

    zone = models.CharField(max_length=50, blank=True, verbose_name="المنطقة")
    aisle = models.CharField(max_length=20, blank=True, verbose_name="الممر")
    rack = models.CharField(max_length=20, blank=True, verbose_name="الرف")
    shelf = models.CharField(max_length=20, blank=True, verbose_name="الطابق")
    bin = models.CharField(max_length=20, blank=True, verbose_name="الصندوق")

    location_code = models.CharField(max_length=100, verbose_name="كود الموقع")

    # حدود المخزون لهذا الموقع
    minimum_stock = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name="الحد الأدنى")
    maximum_stock = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name="الحد الأقصى")

    is_active = models.BooleanField(default=True, verbose_name="نشط")
    is_default = models.BooleanField(default=False, verbose_name="الموقع الافتراضي")

    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشئ بواسطة")

    class Meta:
        verbose_name = "موقع صنف"
        verbose_name_plural = "مواقع الأصناف"
        unique_together = ['warehouse', 'product', 'location_code']

    def __str__(self):
        return f"{self.warehouse.name} - {self.product.name} - {self.location_code}"

# ===== أكواد الأصناف =====
class ProductCode(models.Model):
    """أكواد الأصناف البديلة"""
    CODE_TYPES = [
        ('supplier', 'كود المورد'),
        ('manufacturer', 'كود الشركة المصنعة'),
        ('customer', 'كود العميل'),
        ('internal', 'كود داخلي'),
        ('old_system', 'كود النظام القديم'),
    ]

    product = models.ForeignKey(ProductDefinition, on_delete=models.CASCADE, related_name='alternative_codes', verbose_name="الصنف")
    code_type = models.CharField(max_length=20, choices=CODE_TYPES, verbose_name="نوع الكود")
    code = models.CharField(max_length=100, verbose_name="الكود")
    description = models.CharField(max_length=200, blank=True, verbose_name="الوصف")

    supplier_name = models.CharField(max_length=100, blank=True, verbose_name="اسم المورد")
    is_active = models.BooleanField(default=True, verbose_name="نشط")

    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشئ بواسطة")

    class Meta:
        verbose_name = "كود صنف بديل"
        verbose_name_plural = "أكواد الأصناف البديلة"
        unique_together = ['code_type', 'code']

    def __str__(self):
        return f"{self.product.name} - {self.get_code_type_display()}: {self.code}"

# ===== تعريف البنوك =====
class BankDefinition(models.Model):
    """تعريف البنوك"""
    code = models.CharField(max_length=20, unique=True, verbose_name="كود البنك")
    name = models.CharField(max_length=100, verbose_name="اسم البنك")
    name_en = models.CharField(max_length=100, blank=True, verbose_name="الاسم بالإنجليزية")

    swift_code = models.CharField(max_length=20, blank=True, verbose_name="كود SWIFT")
    routing_number = models.CharField(max_length=20, blank=True, verbose_name="رقم التوجيه")

    address = models.TextField(blank=True, verbose_name="العنوان")
    phone = models.CharField(max_length=20, blank=True, verbose_name="الهاتف")
    email = models.EmailField(blank=True, verbose_name="البريد الإلكتروني")
    website = models.URLField(blank=True, verbose_name="الموقع الإلكتروني")

    contact_person = models.CharField(max_length=100, blank=True, verbose_name="الشخص المسؤول")
    contact_phone = models.CharField(max_length=20, blank=True, verbose_name="هاتف المسؤول")

    is_active = models.BooleanField(default=True, verbose_name="نشط")

    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشئ بواسطة")

    class Meta:
        verbose_name = "تعريف بنك"
        verbose_name_plural = "تعريفات البنوك"
        ordering = ['name']

    def __str__(self):
        return f"{self.code} - {self.name}"

# ===== تعريف العملات =====
class CurrencyDefinition(models.Model):
    """تعريف العملات"""
    code = models.CharField(max_length=3, unique=True, verbose_name="كود العملة")
    name = models.CharField(max_length=100, verbose_name="اسم العملة")
    name_en = models.CharField(max_length=100, blank=True, verbose_name="الاسم بالإنجليزية")
    symbol = models.CharField(max_length=10, verbose_name="رمز العملة")

    exchange_rate = models.DecimalField(max_digits=15, decimal_places=6, default=1, verbose_name="سعر الصرف")
    is_base_currency = models.BooleanField(default=False, verbose_name="العملة الأساسية")

    decimal_places = models.PositiveIntegerField(default=2, validators=[MaxValueValidator(6)], verbose_name="عدد الخانات العشرية")

    is_active = models.BooleanField(default=True, verbose_name="نشط")

    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشئ بواسطة")

    class Meta:
        verbose_name = "تعريف عملة"
        verbose_name_plural = "تعريفات العملات"
        ordering = ['name']

    def __str__(self):
        return f"{self.code} - {self.name} ({self.symbol})"

# ===== تعريف الخزائن =====
class CashBoxDefinition(models.Model):
    """تعريف الخزائن"""
    code = models.CharField(max_length=20, unique=True, verbose_name="كود الخزينة")
    name = models.CharField(max_length=100, verbose_name="اسم الخزينة")
    name_en = models.CharField(max_length=100, blank=True, verbose_name="الاسم بالإنجليزية")

    currency = models.ForeignKey(CurrencyDefinition, on_delete=models.CASCADE, verbose_name="العملة")
    location = models.CharField(max_length=200, blank=True, verbose_name="الموقع")

    opening_balance = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="الرصيد الافتتاحي")
    current_balance = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="الرصيد الحالي")

    # حدود الخزينة
    minimum_balance = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="الحد الأدنى للرصيد")
    maximum_balance = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="الحد الأقصى للرصيد")

    responsible_person = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='managed_cashboxes', verbose_name="المسؤول")

    is_active = models.BooleanField(default=True, verbose_name="نشط")
    allow_negative_balance = models.BooleanField(default=False, verbose_name="السماح بالرصيد السالب")

    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشئ بواسطة")

    class Meta:
        verbose_name = "تعريف خزينة"
        verbose_name_plural = "تعريفات الخزائن"
        ordering = ['name']

    def __str__(self):
        return f"{self.code} - {self.name}"

# ===== مجموعات الأصول =====
class AssetGroup(models.Model):
    """مجموعات الأصول"""
    ASSET_TYPES = [
        ('fixed', 'أصول ثابتة'),
        ('current', 'أصول متداولة'),
        ('intangible', 'أصول غير ملموسة'),
        ('investment', 'استثمارات'),
    ]

    code = models.CharField(max_length=20, unique=True, verbose_name="كود المجموعة")
    name = models.CharField(max_length=100, verbose_name="اسم المجموعة")
    name_en = models.CharField(max_length=100, blank=True, verbose_name="الاسم بالإنجليزية")
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, verbose_name="المجموعة الأب")

    asset_type = models.CharField(max_length=20, choices=ASSET_TYPES, verbose_name="نوع الأصل")

    # معدلات الإهلاك
    depreciation_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0, verbose_name="معدل الإهلاك السنوي %")
    depreciation_method = models.CharField(max_length=20, choices=[
        ('straight_line', 'القسط الثابت'),
        ('declining_balance', 'الرصيد المتناقص'),
        ('sum_of_years', 'مجموع سنوات الخدمة'),
    ], default='straight_line', verbose_name="طريقة الإهلاك")

    useful_life_years = models.PositiveIntegerField(default=5, verbose_name="العمر الافتراضي (سنوات)")

    description = models.TextField(blank=True, verbose_name="الوصف")
    is_active = models.BooleanField(default=True, verbose_name="نشط")

    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشئ بواسطة")

    class Meta:
        verbose_name = "مجموعة أصول"
        verbose_name_plural = "مجموعات الأصول"
        ordering = ['code']

    def __str__(self):
        return f"{self.code} - {self.name}"

    def get_full_path(self):
        """الحصول على المسار الكامل للمجموعة"""
        if self.parent:
            return f"{self.parent.get_full_path()} > {self.name}"
        return self.name

# ===== الأشخاص =====
class PersonDefinition(models.Model):
    """تعريف الأشخاص"""
    PERSON_TYPES = [
        ('customer', 'عميل'),
        ('supplier', 'مورد'),
        ('employee', 'موظف'),
        ('both', 'عميل ومورد'),
        ('other', 'أخرى'),
    ]

    GENDER_CHOICES = [
        ('male', 'ذكر'),
        ('female', 'أنثى'),
    ]

    code = models.CharField(max_length=20, unique=True, verbose_name="كود الشخص")
    name = models.CharField(max_length=200, verbose_name="الاسم")
    name_en = models.CharField(max_length=200, blank=True, verbose_name="الاسم بالإنجليزية")
    person_type = models.CharField(max_length=20, choices=PERSON_TYPES, verbose_name="نوع الشخص")

    # معلومات شخصية
    gender = models.CharField(max_length=10, choices=GENDER_CHOICES, blank=True, verbose_name="الجنس")
    birth_date = models.DateField(null=True, blank=True, verbose_name="تاريخ الميلاد")
    national_id = models.CharField(max_length=20, blank=True, verbose_name="الرقم القومي")
    passport_number = models.CharField(max_length=20, blank=True, verbose_name="رقم الجواز")

    # معلومات الاتصال
    phone = models.CharField(max_length=20, blank=True, verbose_name="الهاتف")
    mobile = models.CharField(max_length=20, blank=True, verbose_name="الجوال")
    email = models.EmailField(blank=True, verbose_name="البريد الإلكتروني")
    website = models.URLField(blank=True, verbose_name="الموقع الإلكتروني")

    # العنوان
    address = models.TextField(blank=True, verbose_name="العنوان")
    city = models.CharField(max_length=100, blank=True, verbose_name="المدينة")
    state = models.CharField(max_length=100, blank=True, verbose_name="المحافظة")
    country = models.CharField(max_length=100, blank=True, verbose_name="الدولة")
    postal_code = models.CharField(max_length=20, blank=True, verbose_name="الرمز البريدي")

    # معلومات مالية
    credit_limit = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="حد الائتمان")
    payment_terms = models.PositiveIntegerField(default=0, verbose_name="شروط الدفع (أيام)")
    currency = models.ForeignKey(CurrencyDefinition, on_delete=models.SET_NULL, null=True, blank=True, verbose_name="العملة")

    # معلومات ضريبية
    tax_number = models.CharField(max_length=50, blank=True, verbose_name="الرقم الضريبي")
    tax_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0, verbose_name="معدل الضريبة %")

    # الحالة
    is_active = models.BooleanField(default=True, verbose_name="نشط")

    # ملاحظات
    notes = models.TextField(blank=True, verbose_name="ملاحظات")

    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشئ بواسطة")

    class Meta:
        verbose_name = "تعريف شخص"
        verbose_name_plural = "تعريفات الأشخاص"
        ordering = ['name']

    def __str__(self):
        return f"{self.code} - {self.name}"

# ===== أنواع المصروفات =====
class ExpenseType(models.Model):
    """أنواع المصروفات"""
    code = models.CharField(max_length=20, unique=True, verbose_name="كود النوع")
    name = models.CharField(max_length=100, verbose_name="اسم النوع")
    name_en = models.CharField(max_length=100, blank=True, verbose_name="الاسم بالإنجليزية")
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, verbose_name="النوع الأب")

    description = models.TextField(blank=True, verbose_name="الوصف")
    is_active = models.BooleanField(default=True, verbose_name="نشط")

    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشئ بواسطة")

    class Meta:
        verbose_name = "نوع مصروف"
        verbose_name_plural = "أنواع المصروفات"
        ordering = ['code']

    def __str__(self):
        return f"{self.code} - {self.name}"

# ===== أسماء المصروفات =====
class ExpenseName(models.Model):
    """أسماء المصروفات"""
    code = models.CharField(max_length=20, unique=True, verbose_name="كود المصروف")
    name = models.CharField(max_length=100, verbose_name="اسم المصروف")
    name_en = models.CharField(max_length=100, blank=True, verbose_name="الاسم بالإنجليزية")
    expense_type = models.ForeignKey(ExpenseType, on_delete=models.CASCADE, verbose_name="نوع المصروف")

    description = models.TextField(blank=True, verbose_name="الوصف")
    default_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="المبلغ الافتراضي")

    is_recurring = models.BooleanField(default=False, verbose_name="مصروف دوري")
    recurrence_period = models.CharField(max_length=20, choices=[
        ('daily', 'يومي'),
        ('weekly', 'أسبوعي'),
        ('monthly', 'شهري'),
        ('quarterly', 'ربع سنوي'),
        ('yearly', 'سنوي'),
    ], blank=True, verbose_name="فترة التكرار")

    is_active = models.BooleanField(default=True, verbose_name="نشط")

    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشئ بواسطة")

    class Meta:
        verbose_name = "اسم مصروف"
        verbose_name_plural = "أسماء المصروفات"
        ordering = ['name']

    def __str__(self):
        return f"{self.code} - {self.name}"

# ===== أنواع الإيرادات =====
class RevenueType(models.Model):
    """أنواع الإيرادات"""
    code = models.CharField(max_length=20, unique=True, verbose_name="كود النوع")
    name = models.CharField(max_length=100, verbose_name="اسم النوع")
    name_en = models.CharField(max_length=100, blank=True, verbose_name="الاسم بالإنجليزية")
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, verbose_name="النوع الأب")

    description = models.TextField(blank=True, verbose_name="الوصف")
    is_active = models.BooleanField(default=True, verbose_name="نشط")

    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشئ بواسطة")

    class Meta:
        verbose_name = "نوع إيراد"
        verbose_name_plural = "أنواع الإيرادات"
        ordering = ['code']

    def __str__(self):
        return f"{self.code} - {self.name}"

# ===== أسماء الإيرادات =====
class RevenueName(models.Model):
    """أسماء الإيرادات"""
    code = models.CharField(max_length=20, unique=True, verbose_name="كود الإيراد")
    name = models.CharField(max_length=100, verbose_name="اسم الإيراد")
    name_en = models.CharField(max_length=100, blank=True, verbose_name="الاسم بالإنجليزية")
    revenue_type = models.ForeignKey(RevenueType, on_delete=models.CASCADE, verbose_name="نوع الإيراد")

    description = models.TextField(blank=True, verbose_name="الوصف")
    default_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="المبلغ الافتراضي")

    is_recurring = models.BooleanField(default=False, verbose_name="إيراد دوري")
    recurrence_period = models.CharField(max_length=20, choices=[
        ('daily', 'يومي'),
        ('weekly', 'أسبوعي'),
        ('monthly', 'شهري'),
        ('quarterly', 'ربع سنوي'),
        ('yearly', 'سنوي'),
    ], blank=True, verbose_name="فترة التكرار")

    is_active = models.BooleanField(default=True, verbose_name="نشط")

    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشئ بواسطة")

    class Meta:
        verbose_name = "اسم إيراد"
        verbose_name_plural = "أسماء الإيرادات"
        ordering = ['name']

    def __str__(self):
        return f"{self.code} - {self.name}"

# ===== مراكز الربحية =====
class ProfitCenter(models.Model):
    """مراكز الربحية"""
    code = models.CharField(max_length=20, unique=True, verbose_name="كود المركز")
    name = models.CharField(max_length=100, verbose_name="اسم المركز")
    name_en = models.CharField(max_length=100, blank=True, verbose_name="الاسم بالإنجليزية")
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, verbose_name="المركز الأب")

    manager = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='managed_profit_centers', verbose_name="المدير")

    description = models.TextField(blank=True, verbose_name="الوصف")
    target_revenue = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="الإيراد المستهدف")
    target_profit = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="الربح المستهدف")

    is_active = models.BooleanField(default=True, verbose_name="نشط")

    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشئ بواسطة")

    class Meta:
        verbose_name = "مركز ربحية"
        verbose_name_plural = "مراكز الربحية"
        ordering = ['code']

    def __str__(self):
        return f"{self.code} - {self.name}"

    def get_full_path(self):
        """الحصول على المسار الكامل للمركز"""
        if self.parent:
            return f"{self.parent.get_full_path()} > {self.name}"
        return self.name

# ===== تعريف الطابعات =====
class PrinterDefinition(models.Model):
    """تعريف الطابعات"""
    PRINTER_TYPES = [
        ('thermal', 'طابعة حرارية'),
        ('inkjet', 'طابعة نفث حبر'),
        ('laser', 'طابعة ليزر'),
        ('dot_matrix', 'طابعة نقطية'),
        ('label', 'طابعة ملصقات'),
    ]

    CONNECTION_TYPES = [
        ('usb', 'USB'),
        ('network', 'شبكة'),
        ('bluetooth', 'بلوتوث'),
        ('serial', 'منفذ تسلسلي'),
        ('parallel', 'منفذ متوازي'),
    ]

    code = models.CharField(max_length=20, unique=True, verbose_name="كود الطابعة")
    name = models.CharField(max_length=100, verbose_name="اسم الطابعة")
    printer_type = models.CharField(max_length=20, choices=PRINTER_TYPES, verbose_name="نوع الطابعة")

    # معلومات الاتصال
    connection_type = models.CharField(max_length=20, choices=CONNECTION_TYPES, verbose_name="نوع الاتصال")
    ip_address = models.GenericIPAddressField(null=True, blank=True, verbose_name="عنوان IP")
    port = models.PositiveIntegerField(null=True, blank=True, verbose_name="المنفذ")
    device_path = models.CharField(max_length=200, blank=True, verbose_name="مسار الجهاز")

    # إعدادات الطباعة
    paper_width = models.PositiveIntegerField(default=80, verbose_name="عرض الورق (مم)")
    paper_height = models.PositiveIntegerField(default=297, verbose_name="طول الورق (مم)")
    dpi = models.PositiveIntegerField(default=203, verbose_name="دقة الطباعة (DPI)")

    # إعدادات متقدمة
    auto_cut = models.BooleanField(default=True, verbose_name="القطع التلقائي")
    cash_drawer = models.BooleanField(default=False, verbose_name="درج النقدية")
    encoding = models.CharField(max_length=20, default='utf-8', verbose_name="ترميز النص")

    # الموقع والاستخدام
    location = models.CharField(max_length=200, blank=True, verbose_name="الموقع")
    department = models.CharField(max_length=100, blank=True, verbose_name="القسم")
    default_for_receipts = models.BooleanField(default=False, verbose_name="افتراضية للإيصالات")
    default_for_reports = models.BooleanField(default=False, verbose_name="افتراضية للتقارير")
    default_for_labels = models.BooleanField(default=False, verbose_name="افتراضية للملصقات")

    is_active = models.BooleanField(default=True, verbose_name="نشط")

    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشئ بواسطة")

    class Meta:
        verbose_name = "تعريف طابعة"
        verbose_name_plural = "تعريفات الطابعات"
        ordering = ['name']

    def __str__(self):
        return f"{self.code} - {self.name}"

    def get_connection_string(self):
        """الحصول على نص الاتصال"""
        if self.connection_type == 'network' and self.ip_address:
            return f"{self.ip_address}:{self.port or 9100}"
        elif self.connection_type in ['usb', 'serial', 'parallel'] and self.device_path:
            return self.device_path
        return "غير محدد"
