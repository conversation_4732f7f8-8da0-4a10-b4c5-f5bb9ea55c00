from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator

class Currency(models.Model):
    """نموذج العملات"""
    name = models.CharField(max_length=100, verbose_name="اسم العملة")
    code = models.CharField(max_length=10, unique=True, verbose_name="رمز العملة")
    symbol = models.CharField(max_length=10, verbose_name="رمز العملة")
    exchange_rate = models.DecimalField(max_digits=10, decimal_places=4, default=1.0, verbose_name="سعر الصرف")
    is_base = models.BooleanField(default=False, verbose_name="العملة الأساسية")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "عملة"
        verbose_name_plural = "العملات"
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.code})"

class Unit(models.Model):
    """نموذج الوحدات"""
    name = models.CharField(max_length=100, verbose_name="اسم الوحدة")
    symbol = models.CharField(max_length=20, verbose_name="رمز الوحدة")
    type_choices = [
        ('weight', 'وزن'),
        ('length', 'طول'),
        ('volume', 'حجم'),
        ('area', 'مساحة'),
        ('quantity', 'كمية'),
        ('time', 'وقت'),
        ('other', 'أخرى'),
    ]
    unit_type = models.CharField(max_length=20, choices=type_choices, verbose_name="نوع الوحدة")
    base_unit = models.ForeignKey('self', on_delete=models.SET_NULL, null=True, blank=True, verbose_name="الوحدة الأساسية")
    conversion_factor = models.DecimalField(max_digits=10, decimal_places=4, default=1.0, verbose_name="معامل التحويل")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "وحدة"
        verbose_name_plural = "الوحدات"
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.symbol})"

class Category(models.Model):
    """نموذج التصنيفات"""
    name = models.CharField(max_length=100, verbose_name="اسم التصنيف")
    code = models.CharField(max_length=20, unique=True, verbose_name="رمز التصنيف")
    description = models.TextField(blank=True, verbose_name="الوصف")
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, verbose_name="التصنيف الأب")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "تصنيف"
        verbose_name_plural = "التصنيفات"
        ordering = ['name']

    def __str__(self):
        return self.name

class PaymentMethod(models.Model):
    """نموذج طرق الدفع"""
    name = models.CharField(max_length=100, verbose_name="اسم طريقة الدفع")
    code = models.CharField(max_length=20, unique=True, verbose_name="رمز طريقة الدفع")
    type_choices = [
        ('cash', 'نقدي'),
        ('bank', 'بنكي'),
        ('credit', 'ائتمان'),
        ('check', 'شيك'),
        ('online', 'إلكتروني'),
    ]
    payment_type = models.CharField(max_length=20, choices=type_choices, verbose_name="نوع الدفع")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "طريقة دفع"
        verbose_name_plural = "طرق الدفع"
        ordering = ['name']

    def __str__(self):
        return self.name

class TaxRate(models.Model):
    """نموذج معدلات الضرائب"""
    name = models.CharField(max_length=100, verbose_name="اسم الضريبة")
    rate = models.DecimalField(max_digits=5, decimal_places=2, validators=[MinValueValidator(0), MaxValueValidator(100)], verbose_name="المعدل %")
    type_choices = [
        ('vat', 'ضريبة القيمة المضافة'),
        ('sales', 'ضريبة المبيعات'),
        ('income', 'ضريبة الدخل'),
        ('other', 'أخرى'),
    ]
    tax_type = models.CharField(max_length=20, choices=type_choices, verbose_name="نوع الضريبة")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "معدل ضريبة"
        verbose_name_plural = "معدلات الضرائب"
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.rate}%)"
